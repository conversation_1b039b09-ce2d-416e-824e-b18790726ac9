#!/bin/bash

echo "Installing JNews Blog dependencies..."

# Check if npm is available
if command -v npm &> /dev/null; then
    echo "Using npm to install dependencies..."
    npm install
elif command -v yarn &> /dev/null; then
    echo "Using yarn to install dependencies..."
    yarn install
elif command -v pnpm &> /dev/null; then
    echo "Using pnpm to install dependencies..."
    pnpm install
else
    echo "No package manager found. Please install npm, yarn, or pnpm."
    exit 1
fi

echo "Dependencies installed successfully!"
echo "To start the development server, run:"
echo "npm run dev (or yarn dev / pnpm dev)"
