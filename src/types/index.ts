export interface Post {
  id: string
  title: string
  excerpt: string
  content: string
  image: string
  author: Author
  category: Category
  tags: string[]
  publishedAt: string
  readTime: number
  views: number
  likes: number
  featured: boolean
  slug: string
}

export interface Author {
  id: string
  name: string
  avatar: string
  bio: string
  social: {
    twitter?: string
    instagram?: string
    linkedin?: string
  }
}

export interface Category {
  id: string
  name: string
  slug: string
  color: string
  description: string
}

export interface SocialReel {
  id: string
  platform: 'instagram' | 'tiktok' | 'youtube'
  url: string
  thumbnail: string
  title: string
  author: string
  views: number
  likes: number
  duration?: number
}

export interface MenuItem {
  id: string
  label: string
  href: string
  children?: MenuItem[]
}
