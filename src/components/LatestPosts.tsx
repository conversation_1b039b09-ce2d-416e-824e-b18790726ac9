'use client'

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Clock, Eye, Heart, ArrowRight } from 'lucide-react'
import { posts } from '@/data/mockData'

const LatestPosts = () => {
  const latestPosts = posts.slice(0, 6)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <div className="space-y-8">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {latestPosts.map((post, index) => (
          <motion.article
            key={post.id}
            variants={itemVariants}
            className="group bg-white rounded-xl shadow-soft overflow-hidden hover-lift"
          >
            <div className="flex flex-col md:flex-row">
              {/* Image */}
              <div className="relative md:w-80 h-48 md:h-auto overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <span
                    className="px-3 py-1 rounded-full text-xs font-semibold text-white"
                    style={{ backgroundColor: post.category.color }}
                  >
                    {post.category.name}
                  </span>
                </div>

                {/* Featured Badge */}
                {post.featured && (
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-yellow-500 text-white rounded-full text-xs font-semibold">
                      ⭐ Featured
                    </span>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 p-6">
                <div className="h-full flex flex-col">
                  {/* Meta Info */}
                  <div className="flex items-center space-x-4 text-sm text-secondary-500 mb-3">
                    <div className="flex items-center space-x-1">
                      <Clock size={14} />
                      <span>{formatDate(post.publishedAt)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye size={14} />
                      <span>{post.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart size={14} />
                      <span>{post.likes}</span>
                    </div>
                  </div>

                  {/* Title */}
                  <Link href={`/post/${post.slug}`}>
                    <h3 className="text-xl md:text-2xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200">
                      {post.title}
                    </h3>
                  </Link>

                  {/* Excerpt */}
                  <p className="text-secondary-600 mb-4 line-clamp-3 leading-relaxed flex-1">
                    {post.excerpt}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs hover:bg-primary-100 hover:text-primary-600 cursor-pointer transition-colors duration-200"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>

                  {/* Author and Read More */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Image
                        src={post.author.avatar}
                        alt={post.author.name}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                      <div>
                        <p className="font-medium text-secondary-900">{post.author.name}</p>
                        <p className="text-sm text-secondary-500">{post.readTime} min read</p>
                      </div>
                    </div>
                    
                    <Link
                      href={`/post/${post.slug}`}
                      className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 group/link"
                    >
                      <span>Read More</span>
                      <ArrowRight size={16} className="group-hover/link:translate-x-1 transition-transform duration-200" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </motion.article>
        ))}
      </motion.div>

      {/* Load More Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="text-center"
      >
        <button className="px-8 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-200 hover:scale-105 shadow-lg">
          Load More Articles
        </button>
      </motion.div>
    </div>
  )
}

export default LatestPosts
