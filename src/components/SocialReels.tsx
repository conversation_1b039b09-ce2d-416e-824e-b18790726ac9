'use client'

import { useState } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, Heart, Eye, ExternalLink, Instagram, Youtube } from 'lucide-react'
import { socialReels } from '@/data/mockData'

const SocialReels = () => {
  const [activeReel, setActiveReel] = useState(0)

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram size={16} className="text-pink-500" />
      case 'youtube':
        return <Youtube size={16} className="text-red-500" />
      case 'tiktok':
        return <div className="w-4 h-4 bg-black rounded-sm flex items-center justify-center">
          <span className="text-white text-xs font-bold">T</span>
        </div>
      default:
        return <Play size={16} />
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return ''
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-4">
      {/* Main Reel Display */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeReel}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="relative aspect-[9/16] max-h-80 rounded-lg overflow-hidden bg-secondary-100 group cursor-pointer"
            onClick={() => window.open(socialReels[activeReel].url, '_blank')}
          >
            <Image
              src={socialReels[activeReel].thumbnail}
              alt={socialReels[activeReel].title}
              fill
              className="object-cover"
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30" />
            
            {/* Play Button */}
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-200"
              >
                <Play size={24} className="text-white ml-1" fill="currentColor" />
              </motion.div>
            </div>

            {/* Platform Badge */}
            <div className="absolute top-3 left-3 flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
              {getPlatformIcon(socialReels[activeReel].platform)}
              <span className="text-white text-xs font-medium capitalize">
                {socialReels[activeReel].platform}
              </span>
            </div>

            {/* Duration */}
            {socialReels[activeReel].duration && (
              <div className="absolute top-3 right-3 bg-black/50 backdrop-blur-sm rounded px-2 py-1">
                <span className="text-white text-xs font-medium">
                  {formatDuration(socialReels[activeReel].duration)}
                </span>
              </div>
            )}

            {/* Content Info */}
            <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
              <h4 className="font-semibold mb-1 line-clamp-2">
                {socialReels[activeReel].title}
              </h4>
              <p className="text-sm text-gray-300 mb-2">
                {socialReels[activeReel].author}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <Eye size={14} />
                    <span>{formatViews(socialReels[activeReel].views)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart size={14} />
                    <span>{formatViews(socialReels[activeReel].likes)}</span>
                  </div>
                </div>
                <ExternalLink size={16} className="opacity-70" />
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Reel Thumbnails */}
      <div className="grid grid-cols-4 gap-2">
        {socialReels.map((reel, index) => (
          <motion.button
            key={reel.id}
            onClick={() => setActiveReel(index)}
            className={`relative aspect-[9/16] rounded-lg overflow-hidden ${
              index === activeReel 
                ? 'ring-2 ring-primary-500' 
                : 'opacity-70 hover:opacity-100'
            } transition-all duration-200`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Image
              src={reel.thumbnail}
              alt={reel.title}
              fill
              className="object-cover"
            />
            
            {/* Platform Icon */}
            <div className="absolute top-1 left-1">
              {getPlatformIcon(reel.platform)}
            </div>

            {/* Duration */}
            {reel.duration && (
              <div className="absolute bottom-1 right-1 bg-black/70 rounded px-1">
                <span className="text-white text-xs">
                  {formatDuration(reel.duration)}
                </span>
              </div>
            )}

            {/* Active Indicator */}
            {index === activeReel && (
              <motion.div
                layoutId="activeReel"
                className="absolute inset-0 bg-primary-500/20 border-2 border-primary-500 rounded-lg"
              />
            )}
          </motion.button>
        ))}
      </div>

      {/* View All Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 flex items-center justify-center space-x-2"
      >
        <span>View All Reels</span>
        <ExternalLink size={16} />
      </motion.button>
    </div>
  )
}

export default SocialReels
