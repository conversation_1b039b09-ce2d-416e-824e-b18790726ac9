'use client'

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Clock, Eye, Heart, User } from 'lucide-react'
import { posts } from '@/data/mockData'

const FeaturedPosts = () => {
  const featuredPosts = posts.filter(post => post.featured).slice(0, 3)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
    >
      {featuredPosts.map((post, index) => (
        <motion.article
          key={post.id}
          variants={itemVariants}
          className="group bg-white rounded-xl shadow-soft overflow-hidden hover-lift"
        >
          {/* Image */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={post.image}
              alt={post.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            
            {/* Category Badge */}
            <div className="absolute top-4 left-4">
              <span
                className="px-3 py-1 rounded-full text-xs font-semibold text-white"
                style={{ backgroundColor: post.category.color }}
              >
                {post.category.name}
              </span>
            </div>

            {/* Featured Badge */}
            {index === 0 && (
              <div className="absolute top-4 right-4">
                <span className="px-3 py-1 bg-yellow-500 text-white rounded-full text-xs font-semibold">
                  ⭐ Featured
                </span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Title */}
            <Link href={`/post/${post.slug}`}>
              <h3 className="text-xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200">
                {post.title}
              </h3>
            </Link>

            {/* Excerpt */}
            <p className="text-secondary-600 mb-4 line-clamp-3 leading-relaxed">
              {post.excerpt}
            </p>

            {/* Meta Information */}
            <div className="flex items-center justify-between text-sm text-secondary-500 mb-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{formatDate(post.publishedAt)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Eye size={14} />
                  <span>{post.views.toLocaleString()}</span>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <Heart size={14} />
                <span>{post.likes}</span>
              </div>
            </div>

            {/* Author */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Image
                  src={post.author.avatar}
                  alt={post.author.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
                <div>
                  <p className="text-sm font-medium text-secondary-900">{post.author.name}</p>
                  <p className="text-xs text-secondary-500">{post.readTime} min read</p>
                </div>
              </div>
              
              {/* Read More Link */}
              <Link
                href={`/post/${post.slug}`}
                className="text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors duration-200"
              >
                Read More →
              </Link>
            </div>
          </div>

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </motion.article>
      ))}
    </motion.div>
  )
}

export default FeaturedPosts
