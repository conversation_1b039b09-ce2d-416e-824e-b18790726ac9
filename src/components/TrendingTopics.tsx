'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Hash, ArrowUp } from 'lucide-react'

const TrendingTopics = () => {
  const trendingTopics = [
    {
      id: 1,
      topic: 'Artificial Intelligence',
      posts: 156,
      trend: '+12%',
      color: '#3b82f6'
    },
    {
      id: 2,
      topic: 'Climate Change',
      posts: 89,
      trend: '+8%',
      color: '#10b981'
    },
    {
      id: 3,
      topic: 'Space Exploration',
      posts: 67,
      trend: '+15%',
      color: '#8b5cf6'
    },
    {
      id: 4,
      topic: 'Sustainable Fashion',
      posts: 45,
      trend: '+6%',
      color: '#ec4899'
    },
    {
      id: 5,
      topic: 'Remote Work',
      posts: 78,
      trend: '+4%',
      color: '#f59e0b'
    },
    {
      id: 6,
      topic: 'Mental Health',
      posts: 92,
      trend: '+9%',
      color: '#ef4444'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.4
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-4"
    >
      {trendingTopics.map((topic, index) => (
        <motion.div
          key={topic.id}
          variants={itemVariants}
          className="group flex items-center justify-between p-3 rounded-lg hover:bg-secondary-50 transition-all duration-200 cursor-pointer"
        >
          <div className="flex items-center space-x-3">
            {/* Rank */}
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-secondary-100 text-secondary-600 font-bold text-sm">
              {index + 1}
            </div>

            {/* Topic Info */}
            <div>
              <div className="flex items-center space-x-2">
                <Hash size={14} style={{ color: topic.color }} />
                <h4 className="font-semibold text-secondary-900 group-hover:text-primary-600 transition-colors duration-200">
                  {topic.topic}
                </h4>
              </div>
              <p className="text-sm text-secondary-500">
                {topic.posts} posts
              </p>
            </div>
          </div>

          {/* Trend Indicator */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-green-600">
              <ArrowUp size={14} />
              <span className="text-sm font-medium">{topic.trend}</span>
            </div>
            <TrendingUp size={16} className="text-secondary-400" />
          </div>
        </motion.div>
      ))}

      {/* View All Button */}
      <motion.button
        variants={itemVariants}
        className="w-full py-3 mt-4 text-primary-600 font-medium hover:bg-primary-50 rounded-lg transition-colors duration-200"
      >
        View All Trending Topics
      </motion.button>
    </motion.div>
  )
}

export default TrendingTopics
