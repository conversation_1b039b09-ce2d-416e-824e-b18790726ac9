'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Send, Check } from 'lucide-react'

const Newsletter = () => {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setIsSubscribed(true)
      setEmail('')
      
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubscribed(false)
      }, 3000)
    }, 1000)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft p-6 text-white relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
      </div>

      <div className="relative z-10">
        {/* Header */}
        <div className="text-center mb-6">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <Mail size={24} />
          </motion.div>
          <h3 className="text-xl font-bold mb-2">Stay in the Loop</h3>
          <p className="text-primary-100 text-sm">
            Get the latest articles and updates delivered straight to your inbox.
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="relative">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              className="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-200"
              disabled={isLoading || isSubscribed}
            />
          </div>

          <motion.button
            type="submit"
            disabled={isLoading || isSubscribed || !email}
            className="w-full py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-primary-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2"
            whileHover={{ scale: isLoading || isSubscribed ? 1 : 1.02 }}
            whileTap={{ scale: isLoading || isSubscribed ? 1 : 0.98 }}
          >
            {isLoading ? (
              <>
                <div className="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Subscribing...</span>
              </>
            ) : isSubscribed ? (
              <>
                <Check size={20} />
                <span>Subscribed!</span>
              </>
            ) : (
              <>
                <Send size={20} />
                <span>Subscribe Now</span>
              </>
            )}
          </motion.button>
        </form>

        {/* Success Message */}
        {isSubscribed && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-3 bg-green-500/20 border border-green-400/30 rounded-lg text-center"
          >
            <p className="text-sm">
              🎉 Welcome aboard! Check your email for confirmation.
            </p>
          </motion.div>
        )}

        {/* Privacy Note */}
        <p className="text-xs text-primary-100 text-center mt-4">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </motion.div>
  )
}

export default Newsletter
