import { Post, Author, Category, SocialReel } from '@/types'

export const authors: Author[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
    bio: 'Tech journalist and digital nomad with 5+ years of experience covering emerging technologies.',
    social: {
      twitter: '@sarahjtech',
      instagram: '@sarahj_tech',
      linkedin: 'sarah-johnson-tech'
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    bio: 'Lifestyle blogger and photographer passionate about travel and culture.',
    social: {
      twitter: '@mikechenphoto',
      instagram: '@mikechenlife'
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    bio: 'Fashion and beauty expert with a keen eye for trends and sustainable living.',
    social: {
      twitter: '@emmarodstyle',
      instagram: '@emma_style_guru'
    }
  }
]

export const categories: Category[] = [
  {
    id: '1',
    name: 'Technology',
    slug: 'technology',
    color: '#3b82f6',
    description: 'Latest tech news, gadgets, and innovations'
  },
  {
    id: '2',
    name: 'Lifestyle',
    slug: 'lifestyle',
    color: '#10b981',
    description: 'Health, wellness, and lifestyle tips'
  },
  {
    id: '3',
    name: 'Travel',
    slug: 'travel',
    color: '#f59e0b',
    description: 'Travel guides, tips, and adventures'
  },
  {
    id: '4',
    name: 'Fashion',
    slug: 'fashion',
    color: '#ec4899',
    description: 'Fashion trends, style guides, and beauty tips'
  },
  {
    id: '5',
    name: 'Food',
    slug: 'food',
    color: '#ef4444',
    description: 'Recipes, restaurant reviews, and culinary adventures'
  }
]

export const posts: Post[] = [
  {
    id: '1',
    title: 'The Future of Artificial Intelligence in Everyday Life',
    excerpt: 'Exploring how AI is transforming our daily routines and what to expect in the coming years.',
    content: `Artificial Intelligence has become an integral part of our daily lives, often in ways we don't even realize. From the moment we wake up to our smart alarm clocks to the personalized recommendations we receive on streaming platforms, AI is quietly working behind the scenes to enhance our experiences.

The rapid advancement of AI technology has brought us to a pivotal moment in history. Machine learning algorithms are becoming more sophisticated, natural language processing is reaching new heights, and computer vision is enabling machines to see and understand the world around them with unprecedented accuracy.

In this comprehensive exploration, we'll dive deep into the current state of AI technology and examine how it's reshaping various aspects of our lives. We'll also look ahead to the future and discuss what we can expect as AI continues to evolve and mature.`,
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
    author: authors[0],
    category: categories[0],
    tags: ['AI', 'Technology', 'Future', 'Innovation'],
    publishedAt: '2024-01-15T10:00:00Z',
    readTime: 8,
    views: 1250,
    likes: 89,
    featured: true,
    slug: 'future-of-artificial-intelligence'
  },
  {
    id: '2',
    title: '10 Hidden Gems in Southeast Asia You Must Visit',
    excerpt: 'Discover breathtaking destinations off the beaten path that will transform your travel experience.',
    content: `Southeast Asia is a treasure trove of incredible destinations, but beyond the well-known tourist hotspots lies a world of hidden gems waiting to be discovered. These lesser-known locations offer authentic cultural experiences, stunning natural beauty, and the chance to explore without the crowds.

From secluded beaches with crystal-clear waters to ancient temples nestled in lush jungles, these hidden gems provide a glimpse into the true heart of Southeast Asia. Each destination on our list offers something unique, whether it's incredible biodiversity, rich cultural heritage, or simply breathtaking natural beauty.

Join us as we embark on a journey to uncover these amazing places that deserve a spot on every traveler's bucket list.`,
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    author: authors[1],
    category: categories[2],
    tags: ['Travel', 'Southeast Asia', 'Hidden Gems', 'Adventure'],
    publishedAt: '2024-01-14T14:30:00Z',
    readTime: 12,
    views: 2100,
    likes: 156,
    featured: true,
    slug: 'hidden-gems-southeast-asia'
  },
  {
    id: '3',
    title: 'Sustainable Fashion: Building a Conscious Wardrobe',
    excerpt: 'Learn how to create a stylish, sustainable wardrobe that reflects your values and reduces environmental impact.',
    content: `The fashion industry is undergoing a significant transformation as consumers become more conscious about the environmental and social impact of their clothing choices. Sustainable fashion is no longer a niche concept but a growing movement that's reshaping how we think about style and consumption.

Building a conscious wardrobe doesn't mean sacrificing style or breaking the bank. It's about making thoughtful choices that align with your values while still expressing your personal style. From choosing quality pieces that last longer to supporting brands with ethical practices, there are many ways to embrace sustainable fashion.

In this guide, we'll explore practical strategies for building a sustainable wardrobe, highlight brands that are leading the way in ethical fashion, and share tips for caring for your clothes to extend their lifespan.`,
    image: 'https://images.unsplash.com/photo-*************-053b83016050?w=800&h=600&fit=crop',
    author: authors[2],
    category: categories[3],
    tags: ['Fashion', 'Sustainability', 'Ethical Fashion', 'Style'],
    publishedAt: '2024-01-13T09:15:00Z',
    readTime: 6,
    views: 890,
    likes: 67,
    featured: false,
    slug: 'sustainable-fashion-conscious-wardrobe'
  }
]

export const socialReels: SocialReel[] = [
  {
    id: '1',
    platform: 'instagram',
    url: 'https://www.instagram.com/reel/example1',
    thumbnail: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=600&fit=crop',
    title: 'Quick Tech Tips for Productivity',
    author: '@techguru',
    views: 15400,
    likes: 892,
    duration: 30
  },
  {
    id: '2',
    platform: 'tiktok',
    url: 'https://www.tiktok.com/@example/video/123',
    thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=600&fit=crop',
    title: 'Travel Packing Hacks',
    author: '@travelhacks',
    views: 28900,
    likes: 1250,
    duration: 45
  },
  {
    id: '3',
    platform: 'youtube',
    url: 'https://www.youtube.com/shorts/example',
    thumbnail: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=600&fit=crop',
    title: 'Fashion Styling Tips',
    author: '@stylequeen',
    views: 8700,
    likes: 456,
    duration: 60
  },
  {
    id: '4',
    platform: 'instagram',
    url: 'https://www.instagram.com/reel/example2',
    thumbnail: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=600&fit=crop',
    title: 'Healthy Recipe in 60 Seconds',
    author: '@healthyfood',
    views: 12300,
    likes: 678,
    duration: 60
  }
]
