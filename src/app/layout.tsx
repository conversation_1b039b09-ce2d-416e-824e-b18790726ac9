import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'JNews Blog - Modern News & Magazine',
  description: 'A modern blog platform with social media integration and beautiful design',
  keywords: 'blog, news, magazine, social media, articles',
  authors: [{ name: 'JNews Blog' }],
  openGraph: {
    title: 'JNews Blog - Modern News & Magazine',
    description: 'A modern blog platform with social media integration and beautiful design',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
