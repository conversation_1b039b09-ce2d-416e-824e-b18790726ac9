'use client'

import { motion } from 'framer-motion'
import HeroSection from '@/components/HeroSection'
import FeaturedPosts from '@/components/FeaturedPosts'
import SocialReels from '@/components/SocialReels'
import LatestPosts from '@/components/LatestPosts'
import TrendingTopics from '@/components/TrendingTopics'
import Newsletter from '@/components/Newsletter'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-12">
            {/* Featured Posts */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-secondary-900">Featured Stories</h2>
                <div className="h-1 flex-1 bg-gradient-to-r from-primary-600 to-transparent ml-6"></div>
              </div>
              <FeaturedPosts />
            </motion.section>

            {/* Latest Posts */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-secondary-900">Latest Articles</h2>
                <div className="h-1 flex-1 bg-gradient-to-r from-primary-600 to-transparent ml-6"></div>
              </div>
              <LatestPosts />
            </motion.section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Social Reels */}
            <motion.section
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="bg-white rounded-xl shadow-soft p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-secondary-900">Social Reels</h3>
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-secondary-500">Live</span>
                  </div>
                </div>
                <SocialReels />
              </div>
            </motion.section>

            {/* Trending Topics */}
            <motion.section
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="bg-white rounded-xl shadow-soft p-6">
                <h3 className="text-xl font-bold text-secondary-900 mb-6">Trending Topics</h3>
                <TrendingTopics />
              </div>
            </motion.section>

            {/* Newsletter Signup */}
            <motion.section
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Newsletter />
            </motion.section>

            {/* Popular Tags */}
            <motion.section
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="bg-white rounded-xl shadow-soft p-6">
                <h3 className="text-xl font-bold text-secondary-900 mb-6">Popular Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {[
                    'Technology', 'AI', 'Travel', 'Fashion', 'Food', 'Health',
                    'Lifestyle', 'Business', 'Science', 'Sports', 'Entertainment'
                  ].map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm hover:bg-primary-100 hover:text-primary-700 cursor-pointer transition-colors duration-200"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </motion.section>

            {/* Weather Widget */}
            <motion.section
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-soft p-6 text-white">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Weather</h3>
                  <span className="text-2xl">☀️</span>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>New York</span>
                    <span className="text-2xl font-bold">72°F</span>
                  </div>
                  <div className="text-blue-100 text-sm">
                    Sunny with clear skies
                  </div>
                  <div className="flex justify-between text-sm text-blue-100">
                    <span>High: 78°F</span>
                    <span>Low: 65°F</span>
                  </div>
                </div>
              </div>
            </motion.section>
          </div>
        </div>
      </div>
    </div>
  )
}
