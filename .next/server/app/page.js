/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FFooter.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FHeader.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FFooter.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FHeader.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGa2FpLWthbCUyMHByb2plY3QlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZrYWkta2FsJTIwcHJvamVjdCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGa2FpLWthbCUyMHByb2plY3QlMkZzcmMlMkZjb21wb25lbnRzJTJGRm9vdGVyLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRmthaS1rYWwlMjBwcm9qZWN0JTJGc3JjJTJGY29tcG9uZW50cyUyRkhlYWRlci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUE2SDtBQUM3SCIsInNvdXJjZXMiOlsid2VicGFjazovL2puZXdzLWJsb2cvPzI5OWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL2thaS1rYWwgcHJvamVjdC9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL2thaS1rYWwgcHJvamVjdC9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FFooter.tsx&modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fcomponents%2FHeader.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGa2FpLWthbCUyMHByb2plY3QlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qbmV3cy1ibG9nLz84MTBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9rYWkta2FsIHByb2plY3Qvc3JjL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_HeroSection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/HeroSection */ \"(ssr)/./src/components/HeroSection.tsx\");\n/* harmony import */ var _components_FeaturedPosts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/FeaturedPosts */ \"(ssr)/./src/components/FeaturedPosts.tsx\");\n/* harmony import */ var _components_SocialReels__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SocialReels */ \"(ssr)/./src/components/SocialReels.tsx\");\n/* harmony import */ var _components_LatestPosts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LatestPosts */ \"(ssr)/./src/components/LatestPosts.tsx\");\n/* harmony import */ var _components_TrendingTopics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/TrendingTopics */ \"(ssr)/./src/components/TrendingTopics.tsx\");\n/* harmony import */ var _components_Newsletter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Newsletter */ \"(ssr)/./src/components/Newsletter.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroSection__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-3xl font-bold text-secondary-900\",\n                                                    children: \"Featured Stories\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-1 flex-1 bg-gradient-to-r from-primary-600 to-transparent ml-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                    lineNumber: 30,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeaturedPosts__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-3xl font-bold text-secondary-900\",\n                                                    children: \"Latest Articles\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-1 flex-1 bg-gradient-to-r from-primary-600 to-transparent ml-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LatestPosts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-soft p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-secondary-900\",\n                                                        children: \"Social Reels\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 61,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-secondary-500\",\n                                                                children: \"Live\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 62,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SocialReels__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-soft p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-secondary-900 mb-6\",\n                                                children: \"Trending Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TrendingTopics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.5\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Newsletter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-soft p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-secondary-900 mb-6\",\n                                                children: \"Popular Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    \"Technology\",\n                                                    \"AI\",\n                                                    \"Travel\",\n                                                    \"Fashion\",\n                                                    \"Food\",\n                                                    \"Health\",\n                                                    \"Lifestyle\",\n                                                    \"Business\",\n                                                    \"Science\",\n                                                    \"Sports\",\n                                                    \"Entertainment\"\n                                                ].map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm hover:bg-primary-100 hover:text-primary-700 cursor-pointer transition-colors duration-200\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, tag, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.7\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-soft p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Weather\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"☀️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"New York\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold\",\n                                                                children: \"72\\xb0F\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-100 text-sm\",\n                                                        children: \"Sunny with clear skies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm text-blue-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"High: 78\\xb0F\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Low: 65\\xb0F\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FeaturedPosts.tsx":
/*!******************************************!*\
  !*** ./src/components/FeaturedPosts.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/mockData */ \"(ssr)/./src/data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst FeaturedPosts = ()=>{\n    const featuredPosts = _data_mockData__WEBPACK_IMPORTED_MODULE_3__.posts.filter((post)=>post.featured).slice(0, 3);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: featuredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                variants: itemVariants,\n                className: \"group bg-white rounded-xl shadow-soft overflow-hidden hover-lift\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-48 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: post.image,\n                                alt: post.title,\n                                fill: true,\n                                className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 left-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 rounded-full text-xs font-semibold text-white\",\n                                    style: {\n                                        backgroundColor: post.category.color\n                                    },\n                                    children: post.category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-yellow-500 text-white rounded-full text-xs font-semibold\",\n                                    children: \"⭐ Featured\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: `/post/${post.slug}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-secondary-600 mb-4 line-clamp-3 leading-relaxed\",\n                                children: post.excerpt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-sm text-secondary-500 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatDate(post.publishedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: post.views.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: post.likes\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                src: post.author.avatar,\n                                                alt: post.author.name,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-secondary-900\",\n                                                        children: post.author.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-secondary-500\",\n                                                        children: [\n                                                            post.readTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/post/${post.slug}`,\n                                        className: \"text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors duration-200\",\n                                        children: \"Read More →\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, post.id, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/FeaturedPosts.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturedPosts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FeaturedPosts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const socialLinks = [\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            href: \"#\",\n            label: \"Facebook\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\",\n            label: \"Twitter\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\",\n            label: \"Instagram\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"#\",\n            label: \"YouTube\"\n        }\n    ];\n    const quickLinks = [\n        {\n            label: \"About Us\",\n            href: \"/about\"\n        },\n        {\n            label: \"Contact\",\n            href: \"/contact\"\n        },\n        {\n            label: \"Privacy Policy\",\n            href: \"/privacy\"\n        },\n        {\n            label: \"Terms of Service\",\n            href: \"/terms\"\n        },\n        {\n            label: \"Advertise\",\n            href: \"/advertise\"\n        },\n        {\n            label: \"Careers\",\n            href: \"/careers\"\n        }\n    ];\n    const categories = [\n        {\n            label: \"Technology\",\n            href: \"/category/technology\"\n        },\n        {\n            label: \"Lifestyle\",\n            href: \"/category/lifestyle\"\n        },\n        {\n            label: \"Travel\",\n            href: \"/category/travel\"\n        },\n        {\n            label: \"Fashion\",\n            href: \"/category/fashion\"\n        },\n        {\n            label: \"Food\",\n            href: \"/category/food\"\n        },\n        {\n            label: \"Health\",\n            href: \"/category/health\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-secondary-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-600 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Stay Updated\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-100 mb-6 max-w-2xl mx-auto\",\n                                children: \"Subscribe to our newsletter and get the latest news, articles, and updates delivered to your inbox.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Enter your email\",\n                                        className: \"flex-1 px-4 py-3 rounded-lg text-secondary-900 outline-none focus:ring-2 focus:ring-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200\",\n                                        children: \"Subscribe\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-xl\",\n                                                    children: \"J\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"JNews\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-secondary-300 mb-4\",\n                                        children: \"Your trusted source for the latest news, insights, and stories that matter. We bring you quality journalism and engaging content across various topics.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: social.href,\n                                                className: \"w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200\",\n                                                \"aria-label\": social.label,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, social.label, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-secondary-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: category.href,\n                                                    className: \"text-secondary-300 hover:text-white transition-colors duration-200\",\n                                                    children: category.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, category.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Contact Info\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 18,\n                                                        className: \"text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-secondary-300\",\n                                                        children: \"123 News Street, NY 10001\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 18,\n                                                        className: \"text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-secondary-300\",\n                                                        children: \"+****************\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 18,\n                                                        className: \"text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-secondary-300\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-secondary-800 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-secondary-400 text-sm\",\n                                children: \"\\xa9 2024 JNews. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Search,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const menuItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Technology\",\n            href: \"/category/technology\"\n        },\n        {\n            label: \"Lifestyle\",\n            href: \"/category/lifestyle\"\n        },\n        {\n            label: \"Travel\",\n            href: \"/category/travel\"\n        },\n        {\n            label: \"Fashion\",\n            href: \"/category/fashion\"\n        },\n        {\n            label: \"Food\",\n            href: \"/category/food\"\n        },\n        {\n            label: \"About\",\n            href: \"/about\"\n        },\n        {\n            label: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-secondary-900 text-white py-2 px-4 text-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\\uD83D\\uDCCD New York, NY\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\\uD83C\\uDF21️ 72\\xb0F\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"\\uD83D\\uDCC5 \",\n                                    new Date().toLocaleDateString(\"en-US\", {\n                                        weekday: \"long\",\n                                        year: \"numeric\",\n                                        month: \"long\",\n                                        day: \"numeric\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n                className: `sticky top-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/95 backdrop-blur-md shadow-lg\" : \"bg-white\"}`,\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"J\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold gradient-text\",\n                                            children: \"JNews\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: \"text-secondary-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group\",\n                                            children: [\n                                                item.label,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, item.href, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSearchOpen(true),\n                                            className: \"p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsDarkMode(!isDarkMode),\n                                            className: \"p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200\",\n                                            children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 31\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 51\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                            className: \"lg:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 31\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: \"auto\"\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"lg:hidden bg-white border-t border-secondary-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-4 space-y-2\",\n                                children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"block py-2 text-secondary-700 hover:text-primary-600 font-medium transition-colors duration-200\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-20\",\n                    onClick: ()=>setIsSearchOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: -50,\n                            opacity: 0\n                        },\n                        animate: {\n                            y: 0,\n                            opacity: 1\n                        },\n                        exit: {\n                            y: -50,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-lg shadow-2xl w-full max-w-2xl mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"text-secondary-400\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search articles, authors, topics...\",\n                                        className: \"flex-1 text-lg outline-none\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsSearchOpen(false),\n                                        className: \"text-secondary-400 hover:text-secondary-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Search_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/mockData */ \"(ssr)/./src/data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst HeroSection = ()=>{\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const featuredPosts = _data_mockData__WEBPACK_IMPORTED_MODULE_4__.posts.filter((post)=>post.featured);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentSlide((prev)=>(prev + 1) % featuredPosts.length);\n        }, 5000);\n        return ()=>clearInterval(timer);\n    }, [\n        featuredPosts.length\n    ]);\n    const nextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % featuredPosts.length);\n    };\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>(prev - 1 + featuredPosts.length) % featuredPosts.length);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"long\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    if (featuredPosts.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative h-[70vh] overflow-hidden bg-secondary-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.7\n                    },\n                    className: \"absolute inset-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: featuredPosts[currentSlide].image,\n                            alt: featuredPosts[currentSlide].title,\n                            fill: true,\n                            className: \"object-cover\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, currentSlide, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16 w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    y: -30,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            scale: 0.8,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.3,\n                                            delay: 0.3\n                                        },\n                                        className: \"inline-block mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-4 py-2 rounded-full text-sm font-semibold text-white\",\n                                            style: {\n                                                backgroundColor: featuredPosts[currentSlide].category.color\n                                            },\n                                            children: featuredPosts[currentSlide].category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h1, {\n                                        initial: {\n                                            y: 20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            y: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.4\n                                        },\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 leading-tight\",\n                                        children: featuredPosts[currentSlide].title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                        initial: {\n                                            y: 20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            y: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.5\n                                        },\n                                        className: \"text-xl text-gray-200 mb-6 leading-relaxed\",\n                                        children: featuredPosts[currentSlide].excerpt\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            y: 20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            y: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.6\n                                        },\n                                        className: \"flex items-center space-x-6 mb-8 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        src: featuredPosts[currentSlide].author.avatar,\n                                                        alt: featuredPosts[currentSlide].author.name,\n                                                        width: 32,\n                                                        height: 32,\n                                                        className: \"rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: featuredPosts[currentSlide].author.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatDate(featuredPosts[currentSlide].publishedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: featuredPosts[currentSlide].views.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: featuredPosts[currentSlide].likes\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            y: 20,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            y: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.7\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: `/post/${featuredPosts[currentSlide].slug}`,\n                                            className: \"inline-flex items-center px-8 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-200 hover:scale-105 shadow-lg\",\n                                            children: [\n                                                \"Read Full Story\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, currentSlide, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: prevSlide,\n                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: nextSlide,\n                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                children: featuredPosts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setCurrentSlide(index),\n                        className: `w-3 h-3 rounded-full transition-all duration-200 ${index === currentSlide ? \"bg-white\" : \"bg-white/50\"}`\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-full h-1 bg-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"h-full bg-primary-500\",\n                    initial: {\n                        width: \"0%\"\n                    },\n                    animate: {\n                        width: \"100%\"\n                    },\n                    transition: {\n                        duration: 5,\n                        ease: \"linear\"\n                    }\n                }, currentSlide, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/HeroSection.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeroSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LatestPosts.tsx":
/*!****************************************!*\
  !*** ./src/components/LatestPosts.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Eye,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/mockData */ \"(ssr)/./src/data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst LatestPosts = ()=>{\n    const latestPosts = _data_mockData__WEBPACK_IMPORTED_MODULE_3__.posts.slice(0, 6);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: \"visible\",\n                className: \"space-y-6\",\n                children: latestPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                        variants: itemVariants,\n                        className: \"group bg-white rounded-xl shadow-soft overflow-hidden hover-lift\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative md:w-80 h-48 md:h-auto overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            src: post.image,\n                                            alt: post.title,\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-xs font-semibold text-white\",\n                                                style: {\n                                                    backgroundColor: post.category.color\n                                                },\n                                                children: post.category.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        post.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 bg-yellow-500 text-white rounded-full text-xs font-semibold\",\n                                                children: \"⭐ Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-secondary-500 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatDate(post.publishedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: post.views.toLocaleString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: post.likes\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/post/${post.slug}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl md:text-2xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200\",\n                                                    children: post.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-secondary-600 mb-4 line-clamp-3 leading-relaxed flex-1\",\n                                                children: post.excerpt\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-4\",\n                                                children: post.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs hover:bg-primary-100 hover:text-primary-600 cursor-pointer transition-colors duration-200\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, tag, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                src: post.author.avatar,\n                                                                alt: post.author.name,\n                                                                width: 40,\n                                                                height: 40,\n                                                                className: \"rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-secondary-900\",\n                                                                        children: post.author.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-secondary-500\",\n                                                                        children: [\n                                                                            post.readTime,\n                                                                            \" min read\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: `/post/${post.slug}`,\n                                                        className: \"flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 group/link\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Read More\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                size: 16,\n                                                                className: \"group-hover/link:translate-x-1 transition-transform duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined)\n                    }, post.id, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.6\n                },\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"px-8 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-200 hover:scale-105 shadow-lg\",\n                    children: \"Load More Articles\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/LatestPosts.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LatestPosts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LatestPosts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Newsletter.tsx":
/*!***************************************!*\
  !*** ./src/components/Newsletter.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Mail,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Mail,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Mail,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Newsletter = ()=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubscribed, setIsSubscribed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        setIsLoading(true);\n        // Simulate API call\n        setTimeout(()=>{\n            setIsLoading(false);\n            setIsSubscribed(true);\n            setEmail(\"\");\n            // Reset after 3 seconds\n            setTimeout(()=>{\n                setIsSubscribed(false);\n            }, 3000);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft p-6 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    scale: 0\n                                },\n                                animate: {\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: \"Stay in the Loop\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-100 text-sm\",\n                                children: \"Get the latest articles and updates delivered straight to your inbox.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    placeholder: \"Enter your email address\",\n                                    className: \"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-200\",\n                                    disabled: isLoading || isSubscribed\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                type: \"submit\",\n                                disabled: isLoading || isSubscribed || !email,\n                                className: \"w-full py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-primary-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2\",\n                                whileHover: {\n                                    scale: isLoading || isSubscribed ? 1 : 1.02\n                                },\n                                whileTap: {\n                                    scale: isLoading || isSubscribed ? 1 : 0.98\n                                },\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Subscribing...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : isSubscribed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Subscribed!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Mail_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Subscribe Now\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    isSubscribed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mt-4 p-3 bg-green-500/20 border border-green-400/30 rounded-lg text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"\\uD83C\\uDF89 Welcome aboard! Check your email for confirmation.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-primary-100 text-center mt-4\",\n                        children: \"We respect your privacy. Unsubscribe at any time.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Newsletter.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Newsletter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Newsletter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SocialReels.tsx":
/*!****************************************!*\
  !*** ./src/components/SocialReels.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart,Instagram,Play,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/mockData */ \"(ssr)/./src/data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SocialReels = ()=>{\n    const [activeReel, setActiveReel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const getPlatformIcon = (platform)=>{\n        switch(platform){\n            case \"instagram\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 16,\n                    className: \"text-pink-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 16\n                }, undefined);\n            case \"youtube\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: 16,\n                    className: \"text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 16\n                }, undefined);\n            case \"tiktok\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 bg-black rounded-sm flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-xs font-bold\",\n                        children: \"T\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const formatViews = (views)=>{\n        if (views >= 1000000) {\n            return `${(views / 1000000).toFixed(1)}M`;\n        } else if (views >= 1000) {\n            return `${(views / 1000).toFixed(1)}K`;\n        }\n        return views.toString();\n    };\n    const formatDuration = (seconds)=>{\n        if (!seconds) return \"\";\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"relative aspect-[9/16] max-h-80 rounded-lg overflow-hidden bg-secondary-100 group cursor-pointer\",\n                        onClick: ()=>window.open(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].url, \"_blank\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].thumbnail,\n                                alt: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].title,\n                                fill: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 24,\n                                        className: \"text-white ml-1\",\n                                        fill: \"currentColor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3 left-3 flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1\",\n                                children: [\n                                    getPlatformIcon(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].platform),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-medium capitalize\",\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].platform\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined),\n                            _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3 right-3 bg-black/50 backdrop-blur-sm rounded px-2 py-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs font-medium\",\n                                    children: formatDuration(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-0 p-4 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-1 line-clamp-2\",\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-300 mb-2\",\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].author\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatViews(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].views)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatViews(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels[activeReel].likes)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 16,\n                                                className: \"opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, activeReel, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-4 gap-2\",\n                children: _data_mockData__WEBPACK_IMPORTED_MODULE_3__.socialReels.map((reel, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                        onClick: ()=>setActiveReel(index),\n                        className: `relative aspect-[9/16] rounded-lg overflow-hidden ${index === activeReel ? \"ring-2 ring-primary-500\" : \"opacity-70 hover:opacity-100\"} transition-all duration-200`,\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: reel.thumbnail,\n                                alt: reel.title,\n                                fill: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1 left-1\",\n                                children: getPlatformIcon(reel.platform)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined),\n                            reel.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1 right-1 bg-black/70 rounded px-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs\",\n                                    children: formatDuration(reel.duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, undefined),\n                            index === activeReel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                layoutId: \"activeReel\",\n                                className: \"absolute inset-0 bg-primary-500/20 border-2 border-primary-500 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, reel.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                className: \"w-full py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 flex items-center justify-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"View All Reels\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_Instagram_Play_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/SocialReels.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocialReels);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Tb2NpYWxSZWVscy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDRjtBQUN5QjtBQUMwQjtBQUNwQztBQUU3QyxNQUFNVyxjQUFjO0lBQ2xCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHYiwrQ0FBUUEsQ0FBQztJQUU3QyxNQUFNYyxrQkFBa0IsQ0FBQ0M7UUFDdkIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDUCx5SEFBU0E7b0JBQUNRLE1BQU07b0JBQUlDLFdBQVU7Ozs7OztZQUN4QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDUix5SEFBT0E7b0JBQUNPLE1BQU07b0JBQUlDLFdBQVU7Ozs7OztZQUN0QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDcEIsNEVBQUNFO3dCQUFLRixXQUFVO2tDQUErQjs7Ozs7Ozs7Ozs7WUFFbkQ7Z0JBQ0UscUJBQU8sOERBQUNiLHlIQUFJQTtvQkFBQ1ksTUFBTTs7Ozs7O1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNSSxjQUFjLENBQUNDO1FBQ25CLElBQUlBLFNBQVMsU0FBUztZQUNwQixPQUFPLENBQUMsRUFBRSxDQUFDQSxRQUFRLE9BQU0sRUFBR0MsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQzNDLE9BQU8sSUFBSUQsU0FBUyxNQUFNO1lBQ3hCLE9BQU8sQ0FBQyxFQUFFLENBQUNBLFFBQVEsSUFBRyxFQUFHQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDeEM7UUFDQSxPQUFPRCxNQUFNRSxRQUFRO0lBQ3ZCO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUksQ0FBQ0EsU0FBUyxPQUFPO1FBQ3JCLE1BQU1DLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0gsVUFBVTtRQUNsQyxNQUFNSSxPQUFPSixVQUFVO1FBQ3ZCLE9BQU8sQ0FBQyxFQUFFQyxLQUFLLENBQUMsRUFBRUcsS0FBS04sUUFBUSxHQUFHTyxRQUFRLENBQUMsR0FBRyxLQUFLLENBQUM7SUFDdEQ7SUFFQSxxQkFDRSw4REFBQ1o7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDZCwwREFBZUE7b0JBQUM0QixNQUFLOzhCQUNwQiw0RUFBQzdCLGlEQUFNQSxDQUFDZ0IsR0FBRzt3QkFFVGMsU0FBUzs0QkFBRUMsU0FBUzs0QkFBR0MsT0FBTzt3QkFBSzt3QkFDbkNDLFNBQVM7NEJBQUVGLFNBQVM7NEJBQUdDLE9BQU87d0JBQUU7d0JBQ2hDRSxNQUFNOzRCQUFFSCxTQUFTOzRCQUFHQyxPQUFPO3dCQUFLO3dCQUNoQ0csWUFBWTs0QkFBRUMsVUFBVTt3QkFBSTt3QkFDNUJyQixXQUFVO3dCQUNWc0IsU0FBUyxJQUFNQyxPQUFPQyxJQUFJLENBQUMvQix1REFBVyxDQUFDRSxXQUFXLENBQUM4QixHQUFHLEVBQUU7OzBDQUV4RCw4REFBQ3pDLG1EQUFLQTtnQ0FDSjBDLEtBQUtqQyx1REFBVyxDQUFDRSxXQUFXLENBQUNnQyxTQUFTO2dDQUN0Q0MsS0FBS25DLHVEQUFXLENBQUNFLFdBQVcsQ0FBQ2tDLEtBQUs7Z0NBQ2xDQyxJQUFJO2dDQUNKOUIsV0FBVTs7Ozs7OzBDQUlaLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7OzBDQUdmLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ2YsaURBQU1BLENBQUNnQixHQUFHO29DQUNUOEIsWUFBWTt3Q0FBRWQsT0FBTztvQ0FBSTtvQ0FDekJlLFVBQVU7d0NBQUVmLE9BQU87b0NBQUs7b0NBQ3hCakIsV0FBVTs4Q0FFViw0RUFBQ2IseUhBQUlBO3dDQUFDWSxNQUFNO3dDQUFJQyxXQUFVO3dDQUFrQjhCLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3JELDhEQUFDN0I7Z0NBQUlELFdBQVU7O29DQUNaSCxnQkFBZ0JKLHVEQUFXLENBQUNFLFdBQVcsQ0FBQ0csUUFBUTtrREFDakQsOERBQUNJO3dDQUFLRixXQUFVO2tEQUNiUCx1REFBVyxDQUFDRSxXQUFXLENBQUNHLFFBQVE7Ozs7Ozs7Ozs7Ozs0QkFLcENMLHVEQUFXLENBQUNFLFdBQVcsQ0FBQzBCLFFBQVEsa0JBQy9CLDhEQUFDcEI7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNFO29DQUFLRixXQUFVOzhDQUNiTyxlQUFlZCx1REFBVyxDQUFDRSxXQUFXLENBQUMwQixRQUFROzs7Ozs7Ozs7OzswQ0FNdEQsOERBQUNwQjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNpQzt3Q0FBR2pDLFdBQVU7a0RBQ1hQLHVEQUFXLENBQUNFLFdBQVcsQ0FBQ2tDLEtBQUs7Ozs7OztrREFFaEMsOERBQUNLO3dDQUFFbEMsV0FBVTtrREFDVlAsdURBQVcsQ0FBQ0UsV0FBVyxDQUFDd0MsTUFBTTs7Ozs7O2tEQUVqQyw4REFBQ2xDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNYLHlIQUFHQTtnRUFBQ1UsTUFBTTs7Ozs7OzBFQUNYLDhEQUFDRzswRUFBTUMsWUFBWVYsdURBQVcsQ0FBQ0UsV0FBVyxDQUFDUyxLQUFLOzs7Ozs7Ozs7Ozs7a0VBRWxELDhEQUFDSDt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNaLDBIQUFLQTtnRUFBQ1csTUFBTTs7Ozs7OzBFQUNiLDhEQUFDRzswRUFBTUMsWUFBWVYsdURBQVcsQ0FBQ0UsV0FBVyxDQUFDeUMsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUdwRCw4REFBQzlDLDBIQUFZQTtnREFBQ1MsTUFBTTtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1QkFqRWpDTDs7Ozs7Ozs7Ozs7Ozs7OzBCQXlFWCw4REFBQ007Z0JBQUlELFdBQVU7MEJBQ1pQLHVEQUFXQSxDQUFDNEMsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUN0Qiw4REFBQ3RELGlEQUFNQSxDQUFDdUQsTUFBTTt3QkFFWmxCLFNBQVMsSUFBTTFCLGNBQWMyQzt3QkFDN0J2QyxXQUFXLENBQUMsa0RBQWtELEVBQzVEdUMsVUFBVTVDLGFBQ04sNEJBQ0EsK0JBQ0wsNEJBQTRCLENBQUM7d0JBQzlCb0MsWUFBWTs0QkFBRWQsT0FBTzt3QkFBSzt3QkFDMUJlLFVBQVU7NEJBQUVmLE9BQU87d0JBQUs7OzBDQUV4Qiw4REFBQ2pDLG1EQUFLQTtnQ0FDSjBDLEtBQUtZLEtBQUtYLFNBQVM7Z0NBQ25CQyxLQUFLVSxLQUFLVCxLQUFLO2dDQUNmQyxJQUFJO2dDQUNKOUIsV0FBVTs7Ozs7OzBDQUlaLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDWkgsZ0JBQWdCeUMsS0FBS3hDLFFBQVE7Ozs7Ozs0QkFJL0J3QyxLQUFLakIsUUFBUSxrQkFDWiw4REFBQ3BCO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDRTtvQ0FBS0YsV0FBVTs4Q0FDYk8sZUFBZStCLEtBQUtqQixRQUFROzs7Ozs7Ozs7Ozs0QkFNbENrQixVQUFVNUMsNEJBQ1QsOERBQUNWLGlEQUFNQSxDQUFDZ0IsR0FBRztnQ0FDVHdDLFVBQVM7Z0NBQ1R6QyxXQUFVOzs7Ozs7O3VCQW5DVHNDLEtBQUtJLEVBQUU7Ozs7Ozs7Ozs7MEJBMkNsQiw4REFBQ3pELGlEQUFNQSxDQUFDdUQsTUFBTTtnQkFDWlQsWUFBWTtvQkFBRWQsT0FBTztnQkFBSztnQkFDMUJlLFVBQVU7b0JBQUVmLE9BQU87Z0JBQUs7Z0JBQ3hCakIsV0FBVTs7a0NBRVYsOERBQUNFO2tDQUFLOzs7Ozs7a0NBQ04sOERBQUNaLDBIQUFZQTt3QkFBQ1MsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTVCO0FBRUEsaUVBQWVMLFdBQVdBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qbmV3cy1ibG9nLy4vc3JjL2NvbXBvbmVudHMvU29jaWFsUmVlbHMudHN4PzFlYTgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IFBsYXksIEhlYXJ0LCBFeWUsIEV4dGVybmFsTGluaywgSW5zdGFncmFtLCBZb3V0dWJlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgc29jaWFsUmVlbHMgfSBmcm9tICdAL2RhdGEvbW9ja0RhdGEnXG5cbmNvbnN0IFNvY2lhbFJlZWxzID0gKCkgPT4ge1xuICBjb25zdCBbYWN0aXZlUmVlbCwgc2V0QWN0aXZlUmVlbF0gPSB1c2VTdGF0ZSgwKVxuXG4gIGNvbnN0IGdldFBsYXRmb3JtSWNvbiA9IChwbGF0Zm9ybTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChwbGF0Zm9ybSkge1xuICAgICAgY2FzZSAnaW5zdGFncmFtJzpcbiAgICAgICAgcmV0dXJuIDxJbnN0YWdyYW0gc2l6ZT17MTZ9IGNsYXNzTmFtZT1cInRleHQtcGluay01MDBcIiAvPlxuICAgICAgY2FzZSAneW91dHViZSc6XG4gICAgICAgIHJldHVybiA8WW91dHViZSBzaXplPXsxNn0gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgIGNhc2UgJ3Rpa3Rvayc6XG4gICAgICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmctYmxhY2sgcm91bmRlZC1zbSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14cyBmb250LWJvbGRcIj5UPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8UGxheSBzaXplPXsxNn0gLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBmb3JtYXRWaWV3cyA9ICh2aWV3czogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHZpZXdzID49IDEwMDAwMDApIHtcbiAgICAgIHJldHVybiBgJHsodmlld3MgLyAxMDAwMDAwKS50b0ZpeGVkKDEpfU1gXG4gICAgfSBlbHNlIGlmICh2aWV3cyA+PSAxMDAwKSB7XG4gICAgICByZXR1cm4gYCR7KHZpZXdzIC8gMTAwMCkudG9GaXhlZCgxKX1LYFxuICAgIH1cbiAgICByZXR1cm4gdmlld3MudG9TdHJpbmcoKVxuICB9XG5cbiAgY29uc3QgZm9ybWF0RHVyYXRpb24gPSAoc2Vjb25kcz86IG51bWJlcikgPT4ge1xuICAgIGlmICghc2Vjb25kcykgcmV0dXJuICcnXG4gICAgY29uc3QgbWlucyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKVxuICAgIGNvbnN0IHNlY3MgPSBzZWNvbmRzICUgNjBcbiAgICByZXR1cm4gYCR7bWluc306JHtzZWNzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1gXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICB7LyogTWFpbiBSZWVsIERpc3BsYXkgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIj5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAga2V5PXthY3RpdmVSZWVsfVxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBhc3BlY3QtWzkvMTZdIG1heC1oLTgwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGJnLXNlY29uZGFyeS0xMDAgZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oc29jaWFsUmVlbHNbYWN0aXZlUmVlbF0udXJsLCAnX2JsYW5rJyl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgIHNyYz17c29jaWFsUmVlbHNbYWN0aXZlUmVlbF0udGh1bWJuYWlsfVxuICAgICAgICAgICAgICBhbHQ9e3NvY2lhbFJlZWxzW2FjdGl2ZVJlZWxdLnRpdGxlfVxuICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogT3ZlcmxheSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNzAgdmlhLXRyYW5zcGFyZW50IHRvLWJsYWNrLzMwXCIgLz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIFBsYXkgQnV0dG9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xIH19XG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctd2hpdGUvMjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ3JvdXAtaG92ZXI6Ymctd2hpdGUvMzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQbGF5IHNpemU9ezI0fSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIG1sLTFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgLz5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBQbGF0Zm9ybSBCYWRnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTMgbGVmdC0zIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCBweC0yIHB5LTFcIj5cbiAgICAgICAgICAgICAge2dldFBsYXRmb3JtSWNvbihzb2NpYWxSZWVsc1thY3RpdmVSZWVsXS5wbGF0Zm9ybSl9XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14cyBmb250LW1lZGl1bSBjYXBpdGFsaXplXCI+XG4gICAgICAgICAgICAgICAge3NvY2lhbFJlZWxzW2FjdGl2ZVJlZWxdLnBsYXRmb3JtfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIER1cmF0aW9uICovfVxuICAgICAgICAgICAge3NvY2lhbFJlZWxzW2FjdGl2ZVJlZWxdLmR1cmF0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMyByaWdodC0zIGJnLWJsYWNrLzUwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZCBweC0yIHB5LTFcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIHtmb3JtYXREdXJhdGlvbihzb2NpYWxSZWVsc1thY3RpdmVSZWVsXS5kdXJhdGlvbil9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBDb250ZW50IEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIHAtNCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTEgbGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICAgICAge3NvY2lhbFJlZWxzW2FjdGl2ZVJlZWxdLnRpdGxlfVxuICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIHtzb2NpYWxSZWVsc1thY3RpdmVSZWVsXS5hdXRob3J9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8RXllIHNpemU9ezE0fSAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Zm9ybWF0Vmlld3Moc29jaWFsUmVlbHNbYWN0aXZlUmVlbF0udmlld3MpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPEhlYXJ0IHNpemU9ezE0fSAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Zm9ybWF0Vmlld3Moc29jaWFsUmVlbHNbYWN0aXZlUmVlbF0ubGlrZXMpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgc2l6ZT17MTZ9IGNsYXNzTmFtZT1cIm9wYWNpdHktNzBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlZWwgVGh1bWJuYWlscyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNCBnYXAtMlwiPlxuICAgICAgICB7c29jaWFsUmVlbHMubWFwKChyZWVsLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICBrZXk9e3JlZWwuaWR9XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVSZWVsKGluZGV4KX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIGFzcGVjdC1bOS8xNl0gcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gJHtcbiAgICAgICAgICAgICAgaW5kZXggPT09IGFjdGl2ZVJlZWwgXG4gICAgICAgICAgICAgICAgPyAncmluZy0yIHJpbmctcHJpbWFyeS01MDAnIFxuICAgICAgICAgICAgICAgIDogJ29wYWNpdHktNzAgaG92ZXI6b3BhY2l0eS0xMDAnXG4gICAgICAgICAgICB9IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMGB9XG4gICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICBzcmM9e3JlZWwudGh1bWJuYWlsfVxuICAgICAgICAgICAgICBhbHQ9e3JlZWwudGl0bGV9XG4gICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBQbGF0Zm9ybSBJY29uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMSBsZWZ0LTFcIj5cbiAgICAgICAgICAgICAge2dldFBsYXRmb3JtSWNvbihyZWVsLnBsYXRmb3JtKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRHVyYXRpb24gKi99XG4gICAgICAgICAgICB7cmVlbC5kdXJhdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTEgcmlnaHQtMSBiZy1ibGFjay83MCByb3VuZGVkIHB4LTFcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgIHtmb3JtYXREdXJhdGlvbihyZWVsLmR1cmF0aW9uKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEFjdGl2ZSBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICB7aW5kZXggPT09IGFjdGl2ZVJlZWwgJiYgKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGxheW91dElkPVwiYWN0aXZlUmVlbFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1wcmltYXJ5LTUwMC8yMCBib3JkZXItMiBib3JkZXItcHJpbWFyeS01MDAgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFZpZXcgQWxsIEJ1dHRvbiAqL31cbiAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB5LTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnktNTAwIHRvLXByaW1hcnktNjAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcm91bmRlZC1sZyBob3Zlcjpmcm9tLXByaW1hcnktNjAwIGhvdmVyOnRvLXByaW1hcnktNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgPlxuICAgICAgICA8c3Bhbj5WaWV3IEFsbCBSZWVsczwvc3Bhbj5cbiAgICAgICAgPEV4dGVybmFsTGluayBzaXplPXsxNn0gLz5cbiAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBTb2NpYWxSZWVsc1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiSW1hZ2UiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJQbGF5IiwiSGVhcnQiLCJFeWUiLCJFeHRlcm5hbExpbmsiLCJJbnN0YWdyYW0iLCJZb3V0dWJlIiwic29jaWFsUmVlbHMiLCJTb2NpYWxSZWVscyIsImFjdGl2ZVJlZWwiLCJzZXRBY3RpdmVSZWVsIiwiZ2V0UGxhdGZvcm1JY29uIiwicGxhdGZvcm0iLCJzaXplIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsImZvcm1hdFZpZXdzIiwidmlld3MiLCJ0b0ZpeGVkIiwidG9TdHJpbmciLCJmb3JtYXREdXJhdGlvbiIsInNlY29uZHMiLCJtaW5zIiwiTWF0aCIsImZsb29yIiwic2VjcyIsInBhZFN0YXJ0IiwibW9kZSIsImluaXRpYWwiLCJvcGFjaXR5Iiwic2NhbGUiLCJhbmltYXRlIiwiZXhpdCIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsIm9uQ2xpY2siLCJ3aW5kb3ciLCJvcGVuIiwidXJsIiwic3JjIiwidGh1bWJuYWlsIiwiYWx0IiwidGl0bGUiLCJmaWxsIiwid2hpbGVIb3ZlciIsIndoaWxlVGFwIiwiaDQiLCJwIiwiYXV0aG9yIiwibGlrZXMiLCJtYXAiLCJyZWVsIiwiaW5kZXgiLCJidXR0b24iLCJsYXlvdXRJZCIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SocialReels.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TrendingTopics.tsx":
/*!*******************************************!*\
  !*** ./src/components/TrendingTopics.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Hash,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Hash,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Hash,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst TrendingTopics = ()=>{\n    const trendingTopics = [\n        {\n            id: 1,\n            topic: \"Artificial Intelligence\",\n            posts: 156,\n            trend: \"+12%\",\n            color: \"#3b82f6\"\n        },\n        {\n            id: 2,\n            topic: \"Climate Change\",\n            posts: 89,\n            trend: \"+8%\",\n            color: \"#10b981\"\n        },\n        {\n            id: 3,\n            topic: \"Space Exploration\",\n            posts: 67,\n            trend: \"+15%\",\n            color: \"#8b5cf6\"\n        },\n        {\n            id: 4,\n            topic: \"Sustainable Fashion\",\n            posts: 45,\n            trend: \"+6%\",\n            color: \"#ec4899\"\n        },\n        {\n            id: 5,\n            topic: \"Remote Work\",\n            posts: 78,\n            trend: \"+4%\",\n            color: \"#f59e0b\"\n        },\n        {\n            id: 6,\n            topic: \"Mental Health\",\n            posts: 92,\n            trend: \"+9%\",\n            color: \"#ef4444\"\n        }\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            x: -20\n        },\n        visible: {\n            opacity: 1,\n            x: 0,\n            transition: {\n                duration: 0.4\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        className: \"space-y-4\",\n        children: [\n            trendingTopics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: itemVariants,\n                    className: \"group flex items-center justify-between p-3 rounded-lg hover:bg-secondary-50 transition-all duration-200 cursor-pointer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center w-8 h-8 rounded-full bg-secondary-100 text-secondary-600 font-bold text-sm\",\n                                    children: index + 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    size: 14,\n                                                    style: {\n                                                        color: topic.color\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-secondary-900 group-hover:text-primary-600 transition-colors duration-200\",\n                                                    children: topic.topic\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-secondary-500\",\n                                            children: [\n                                                topic.posts,\n                                                \" posts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 text-green-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: topic.trend\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Hash_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 16,\n                                    className: \"text-secondary-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, topic.id, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                variants: itemVariants,\n                className: \"w-full py-3 mt-4 text-primary-600 font-medium hover:bg-primary-50 rounded-lg transition-colors duration-200\",\n                children: \"View All Trending Topics\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/TrendingTopics.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TrendingTopics);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TrendingTopics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/mockData.ts":
/*!******************************!*\
  !*** ./src/data/mockData.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authors: () => (/* binding */ authors),\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   posts: () => (/* binding */ posts),\n/* harmony export */   socialReels: () => (/* binding */ socialReels)\n/* harmony export */ });\nconst authors = [\n    {\n        id: \"1\",\n        name: \"Sarah Johnson\",\n        avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face\",\n        bio: \"Tech journalist and digital nomad with 5+ years of experience covering emerging technologies.\",\n        social: {\n            twitter: \"@sarahjtech\",\n            instagram: \"@sarahj_tech\",\n            linkedin: \"sarah-johnson-tech\"\n        }\n    },\n    {\n        id: \"2\",\n        name: \"Michael Chen\",\n        avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n        bio: \"Lifestyle blogger and photographer passionate about travel and culture.\",\n        social: {\n            twitter: \"@mikechenphoto\",\n            instagram: \"@mikechenlife\"\n        }\n    },\n    {\n        id: \"3\",\n        name: \"Emma Rodriguez\",\n        avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n        bio: \"Fashion and beauty expert with a keen eye for trends and sustainable living.\",\n        social: {\n            twitter: \"@emmarodstyle\",\n            instagram: \"@emma_style_guru\"\n        }\n    }\n];\nconst categories = [\n    {\n        id: \"1\",\n        name: \"Technology\",\n        slug: \"technology\",\n        color: \"#3b82f6\",\n        description: \"Latest tech news, gadgets, and innovations\"\n    },\n    {\n        id: \"2\",\n        name: \"Lifestyle\",\n        slug: \"lifestyle\",\n        color: \"#10b981\",\n        description: \"Health, wellness, and lifestyle tips\"\n    },\n    {\n        id: \"3\",\n        name: \"Travel\",\n        slug: \"travel\",\n        color: \"#f59e0b\",\n        description: \"Travel guides, tips, and adventures\"\n    },\n    {\n        id: \"4\",\n        name: \"Fashion\",\n        slug: \"fashion\",\n        color: \"#ec4899\",\n        description: \"Fashion trends, style guides, and beauty tips\"\n    },\n    {\n        id: \"5\",\n        name: \"Food\",\n        slug: \"food\",\n        color: \"#ef4444\",\n        description: \"Recipes, restaurant reviews, and culinary adventures\"\n    }\n];\nconst posts = [\n    {\n        id: \"1\",\n        title: \"The Future of Artificial Intelligence in Everyday Life\",\n        excerpt: \"Exploring how AI is transforming our daily routines and what to expect in the coming years.\",\n        content: `Artificial Intelligence has become an integral part of our daily lives, often in ways we don't even realize. From the moment we wake up to our smart alarm clocks to the personalized recommendations we receive on streaming platforms, AI is quietly working behind the scenes to enhance our experiences.\n\nThe rapid advancement of AI technology has brought us to a pivotal moment in history. Machine learning algorithms are becoming more sophisticated, natural language processing is reaching new heights, and computer vision is enabling machines to see and understand the world around them with unprecedented accuracy.\n\nIn this comprehensive exploration, we'll dive deep into the current state of AI technology and examine how it's reshaping various aspects of our lives. We'll also look ahead to the future and discuss what we can expect as AI continues to evolve and mature.`,\n        image: \"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop\",\n        author: authors[0],\n        category: categories[0],\n        tags: [\n            \"AI\",\n            \"Technology\",\n            \"Future\",\n            \"Innovation\"\n        ],\n        publishedAt: \"2024-01-15T10:00:00Z\",\n        readTime: 8,\n        views: 1250,\n        likes: 89,\n        featured: true,\n        slug: \"future-of-artificial-intelligence\"\n    },\n    {\n        id: \"2\",\n        title: \"10 Hidden Gems in Southeast Asia You Must Visit\",\n        excerpt: \"Discover breathtaking destinations off the beaten path that will transform your travel experience.\",\n        content: `Southeast Asia is a treasure trove of incredible destinations, but beyond the well-known tourist hotspots lies a world of hidden gems waiting to be discovered. These lesser-known locations offer authentic cultural experiences, stunning natural beauty, and the chance to explore without the crowds.\n\nFrom secluded beaches with crystal-clear waters to ancient temples nestled in lush jungles, these hidden gems provide a glimpse into the true heart of Southeast Asia. Each destination on our list offers something unique, whether it's incredible biodiversity, rich cultural heritage, or simply breathtaking natural beauty.\n\nJoin us as we embark on a journey to uncover these amazing places that deserve a spot on every traveler's bucket list.`,\n        image: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop\",\n        author: authors[1],\n        category: categories[2],\n        tags: [\n            \"Travel\",\n            \"Southeast Asia\",\n            \"Hidden Gems\",\n            \"Adventure\"\n        ],\n        publishedAt: \"2024-01-14T14:30:00Z\",\n        readTime: 12,\n        views: 2100,\n        likes: 156,\n        featured: true,\n        slug: \"hidden-gems-southeast-asia\"\n    },\n    {\n        id: \"3\",\n        title: \"Sustainable Fashion: Building a Conscious Wardrobe\",\n        excerpt: \"Learn how to create a stylish, sustainable wardrobe that reflects your values and reduces environmental impact.\",\n        content: `The fashion industry is undergoing a significant transformation as consumers become more conscious about the environmental and social impact of their clothing choices. Sustainable fashion is no longer a niche concept but a growing movement that's reshaping how we think about style and consumption.\n\nBuilding a conscious wardrobe doesn't mean sacrificing style or breaking the bank. It's about making thoughtful choices that align with your values while still expressing your personal style. From choosing quality pieces that last longer to supporting brands with ethical practices, there are many ways to embrace sustainable fashion.\n\nIn this guide, we'll explore practical strategies for building a sustainable wardrobe, highlight brands that are leading the way in ethical fashion, and share tips for caring for your clothes to extend their lifespan.`,\n        image: \"https://images.unsplash.com/photo-*************-053b83016050?w=800&h=600&fit=crop\",\n        author: authors[2],\n        category: categories[3],\n        tags: [\n            \"Fashion\",\n            \"Sustainability\",\n            \"Ethical Fashion\",\n            \"Style\"\n        ],\n        publishedAt: \"2024-01-13T09:15:00Z\",\n        readTime: 6,\n        views: 890,\n        likes: 67,\n        featured: false,\n        slug: \"sustainable-fashion-conscious-wardrobe\"\n    }\n];\nconst socialReels = [\n    {\n        id: \"1\",\n        platform: \"instagram\",\n        url: \"https://www.instagram.com/reel/example1\",\n        thumbnail: \"https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=600&fit=crop\",\n        title: \"Quick Tech Tips for Productivity\",\n        author: \"@techguru\",\n        views: 15400,\n        likes: 892,\n        duration: 30\n    },\n    {\n        id: \"2\",\n        platform: \"tiktok\",\n        url: \"https://www.tiktok.com/@example/video/123\",\n        thumbnail: \"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=600&fit=crop\",\n        title: \"Travel Packing Hacks\",\n        author: \"@travelhacks\",\n        views: 28900,\n        likes: 1250,\n        duration: 45\n    },\n    {\n        id: \"3\",\n        platform: \"youtube\",\n        url: \"https://www.youtube.com/shorts/example\",\n        thumbnail: \"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=600&fit=crop\",\n        title: \"Fashion Styling Tips\",\n        author: \"@stylequeen\",\n        views: 8700,\n        likes: 456,\n        duration: 60\n    },\n    {\n        id: \"4\",\n        platform: \"instagram\",\n        url: \"https://www.instagram.com/reel/example2\",\n        thumbnail: \"https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=600&fit=crop\",\n        title: \"Healthy Recipe in 60 Seconds\",\n        author: \"@healthyfood\",\n        views: 12300,\n        likes: 678,\n        duration: 60\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZGF0YS9tb2NrRGF0YS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRU8sTUFBTUEsVUFBb0I7SUFDL0I7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMQyxRQUFRO1lBQ05DLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxVQUFVO1FBQ1o7SUFDRjtJQUNBO1FBQ0VQLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsUUFBUTtZQUNOQyxTQUFTO1lBQ1RDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMQyxRQUFRO1lBQ05DLFNBQVM7WUFDVEMsV0FBVztRQUNiO0lBQ0Y7Q0FDRDtBQUVNLE1BQU1FLGFBQXlCO0lBQ3BDO1FBQ0VSLElBQUk7UUFDSkMsTUFBTTtRQUNOUSxNQUFNO1FBQ05DLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0E7UUFDRVgsSUFBSTtRQUNKQyxNQUFNO1FBQ05RLE1BQU07UUFDTkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFWCxJQUFJO1FBQ0pDLE1BQU07UUFDTlEsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBO1FBQ0VYLElBQUk7UUFDSkMsTUFBTTtRQUNOUSxNQUFNO1FBQ05DLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0E7UUFDRVgsSUFBSTtRQUNKQyxNQUFNO1FBQ05RLE1BQU07UUFDTkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7Q0FDRDtBQUVNLE1BQU1DLFFBQWdCO0lBQzNCO1FBQ0VaLElBQUk7UUFDSmEsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVMsQ0FBQzs7OztnUUFJa1AsQ0FBQztRQUM3UEMsT0FBTztRQUNQQyxRQUFRbEIsT0FBTyxDQUFDLEVBQUU7UUFDbEJtQixVQUFVVixVQUFVLENBQUMsRUFBRTtRQUN2QlcsTUFBTTtZQUFDO1lBQU07WUFBYztZQUFVO1NBQWE7UUFDbERDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsVUFBVTtRQUNWZixNQUFNO0lBQ1I7SUFDQTtRQUNFVCxJQUFJO1FBQ0phLE9BQU87UUFDUEMsU0FBUztRQUNUQyxTQUFTLENBQUM7Ozs7c0hBSXdHLENBQUM7UUFDbkhDLE9BQU87UUFDUEMsUUFBUWxCLE9BQU8sQ0FBQyxFQUFFO1FBQ2xCbUIsVUFBVVYsVUFBVSxDQUFDLEVBQUU7UUFDdkJXLE1BQU07WUFBQztZQUFVO1lBQWtCO1lBQWU7U0FBWTtRQUM5REMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZmLE1BQU07SUFDUjtJQUNBO1FBQ0VULElBQUk7UUFDSmEsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVMsQ0FBQzs7Ozt5TkFJMk0sQ0FBQztRQUN0TkMsT0FBTztRQUNQQyxRQUFRbEIsT0FBTyxDQUFDLEVBQUU7UUFDbEJtQixVQUFVVixVQUFVLENBQUMsRUFBRTtRQUN2QlcsTUFBTTtZQUFDO1lBQVc7WUFBa0I7WUFBbUI7U0FBUTtRQUMvREMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZmLE1BQU07SUFDUjtDQUNEO0FBRU0sTUFBTWdCLGNBQTRCO0lBQ3ZDO1FBQ0V6QixJQUFJO1FBQ0owQixVQUFVO1FBQ1ZDLEtBQUs7UUFDTEMsV0FBVztRQUNYZixPQUFPO1FBQ1BJLFFBQVE7UUFDUkssT0FBTztRQUNQQyxPQUFPO1FBQ1BNLFVBQVU7SUFDWjtJQUNBO1FBQ0U3QixJQUFJO1FBQ0owQixVQUFVO1FBQ1ZDLEtBQUs7UUFDTEMsV0FBVztRQUNYZixPQUFPO1FBQ1BJLFFBQVE7UUFDUkssT0FBTztRQUNQQyxPQUFPO1FBQ1BNLFVBQVU7SUFDWjtJQUNBO1FBQ0U3QixJQUFJO1FBQ0owQixVQUFVO1FBQ1ZDLEtBQUs7UUFDTEMsV0FBVztRQUNYZixPQUFPO1FBQ1BJLFFBQVE7UUFDUkssT0FBTztRQUNQQyxPQUFPO1FBQ1BNLFVBQVU7SUFDWjtJQUNBO1FBQ0U3QixJQUFJO1FBQ0owQixVQUFVO1FBQ1ZDLEtBQUs7UUFDTEMsV0FBVztRQUNYZixPQUFPO1FBQ1BJLFFBQVE7UUFDUkssT0FBTztRQUNQQyxPQUFPO1FBQ1BNLFVBQVU7SUFDWjtDQUNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam5ld3MtYmxvZy8uL3NyYy9kYXRhL21vY2tEYXRhLnRzPzU4OGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUG9zdCwgQXV0aG9yLCBDYXRlZ29yeSwgU29jaWFsUmVlbCB9IGZyb20gJ0AvdHlwZXMnXG5cbmV4cG9ydCBjb25zdCBhdXRob3JzOiBBdXRob3JbXSA9IFtcbiAge1xuICAgIGlkOiAnMScsXG4gICAgbmFtZTogJ1NhcmFoIEpvaG5zb24nLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk0NzkwMTA4NzU1LTI2MTZiNjEyYjQ3Yz93PTE1MCZoPTE1MCZmaXQ9Y3JvcCZjcm9wPWZhY2UnLFxuICAgIGJpbzogJ1RlY2ggam91cm5hbGlzdCBhbmQgZGlnaXRhbCBub21hZCB3aXRoIDUrIHllYXJzIG9mIGV4cGVyaWVuY2UgY292ZXJpbmcgZW1lcmdpbmcgdGVjaG5vbG9naWVzLicsXG4gICAgc29jaWFsOiB7XG4gICAgICB0d2l0dGVyOiAnQHNhcmFoanRlY2gnLFxuICAgICAgaW5zdGFncmFtOiAnQHNhcmFoal90ZWNoJyxcbiAgICAgIGxpbmtlZGluOiAnc2FyYWgtam9obnNvbi10ZWNoJ1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgbmFtZTogJ01pY2hhZWwgQ2hlbicsXG4gICAgYXZhdGFyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDcwMDMyMTExNjktMGExZGQ3MjI4ZjJkP3c9MTUwJmg9MTUwJmZpdD1jcm9wJmNyb3A9ZmFjZScsXG4gICAgYmlvOiAnTGlmZXN0eWxlIGJsb2dnZXIgYW5kIHBob3RvZ3JhcGhlciBwYXNzaW9uYXRlIGFib3V0IHRyYXZlbCBhbmQgY3VsdHVyZS4nLFxuICAgIHNvY2lhbDoge1xuICAgICAgdHdpdHRlcjogJ0BtaWtlY2hlbnBob3RvJyxcbiAgICAgIGluc3RhZ3JhbTogJ0BtaWtlY2hlbmxpZmUnXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBuYW1lOiAnRW1tYSBSb2RyaWd1ZXonLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDM4NzYxNjgxMDMzLTY0NjFmZmFkOGQ4MD93PTE1MCZoPTE1MCZmaXQ9Y3JvcCZjcm9wPWZhY2UnLFxuICAgIGJpbzogJ0Zhc2hpb24gYW5kIGJlYXV0eSBleHBlcnQgd2l0aCBhIGtlZW4gZXllIGZvciB0cmVuZHMgYW5kIHN1c3RhaW5hYmxlIGxpdmluZy4nLFxuICAgIHNvY2lhbDoge1xuICAgICAgdHdpdHRlcjogJ0BlbW1hcm9kc3R5bGUnLFxuICAgICAgaW5zdGFncmFtOiAnQGVtbWFfc3R5bGVfZ3VydSdcbiAgICB9XG4gIH1cbl1cblxuZXhwb3J0IGNvbnN0IGNhdGVnb3JpZXM6IENhdGVnb3J5W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIG5hbWU6ICdUZWNobm9sb2d5JyxcbiAgICBzbHVnOiAndGVjaG5vbG9neScsXG4gICAgY29sb3I6ICcjM2I4MmY2JyxcbiAgICBkZXNjcmlwdGlvbjogJ0xhdGVzdCB0ZWNoIG5ld3MsIGdhZGdldHMsIGFuZCBpbm5vdmF0aW9ucydcbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgbmFtZTogJ0xpZmVzdHlsZScsXG4gICAgc2x1ZzogJ2xpZmVzdHlsZScsXG4gICAgY29sb3I6ICcjMTBiOTgxJyxcbiAgICBkZXNjcmlwdGlvbjogJ0hlYWx0aCwgd2VsbG5lc3MsIGFuZCBsaWZlc3R5bGUgdGlwcydcbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgbmFtZTogJ1RyYXZlbCcsXG4gICAgc2x1ZzogJ3RyYXZlbCcsXG4gICAgY29sb3I6ICcjZjU5ZTBiJyxcbiAgICBkZXNjcmlwdGlvbjogJ1RyYXZlbCBndWlkZXMsIHRpcHMsIGFuZCBhZHZlbnR1cmVzJ1xuICB9LFxuICB7XG4gICAgaWQ6ICc0JyxcbiAgICBuYW1lOiAnRmFzaGlvbicsXG4gICAgc2x1ZzogJ2Zhc2hpb24nLFxuICAgIGNvbG9yOiAnI2VjNDg5OScsXG4gICAgZGVzY3JpcHRpb246ICdGYXNoaW9uIHRyZW5kcywgc3R5bGUgZ3VpZGVzLCBhbmQgYmVhdXR5IHRpcHMnXG4gIH0sXG4gIHtcbiAgICBpZDogJzUnLFxuICAgIG5hbWU6ICdGb29kJyxcbiAgICBzbHVnOiAnZm9vZCcsXG4gICAgY29sb3I6ICcjZWY0NDQ0JyxcbiAgICBkZXNjcmlwdGlvbjogJ1JlY2lwZXMsIHJlc3RhdXJhbnQgcmV2aWV3cywgYW5kIGN1bGluYXJ5IGFkdmVudHVyZXMnXG4gIH1cbl1cblxuZXhwb3J0IGNvbnN0IHBvc3RzOiBQb3N0W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHRpdGxlOiAnVGhlIEZ1dHVyZSBvZiBBcnRpZmljaWFsIEludGVsbGlnZW5jZSBpbiBFdmVyeWRheSBMaWZlJyxcbiAgICBleGNlcnB0OiAnRXhwbG9yaW5nIGhvdyBBSSBpcyB0cmFuc2Zvcm1pbmcgb3VyIGRhaWx5IHJvdXRpbmVzIGFuZCB3aGF0IHRvIGV4cGVjdCBpbiB0aGUgY29taW5nIHllYXJzLicsXG4gICAgY29udGVudDogYEFydGlmaWNpYWwgSW50ZWxsaWdlbmNlIGhhcyBiZWNvbWUgYW4gaW50ZWdyYWwgcGFydCBvZiBvdXIgZGFpbHkgbGl2ZXMsIG9mdGVuIGluIHdheXMgd2UgZG9uJ3QgZXZlbiByZWFsaXplLiBGcm9tIHRoZSBtb21lbnQgd2Ugd2FrZSB1cCB0byBvdXIgc21hcnQgYWxhcm0gY2xvY2tzIHRvIHRoZSBwZXJzb25hbGl6ZWQgcmVjb21tZW5kYXRpb25zIHdlIHJlY2VpdmUgb24gc3RyZWFtaW5nIHBsYXRmb3JtcywgQUkgaXMgcXVpZXRseSB3b3JraW5nIGJlaGluZCB0aGUgc2NlbmVzIHRvIGVuaGFuY2Ugb3VyIGV4cGVyaWVuY2VzLlxuXG5UaGUgcmFwaWQgYWR2YW5jZW1lbnQgb2YgQUkgdGVjaG5vbG9neSBoYXMgYnJvdWdodCB1cyB0byBhIHBpdm90YWwgbW9tZW50IGluIGhpc3RvcnkuIE1hY2hpbmUgbGVhcm5pbmcgYWxnb3JpdGhtcyBhcmUgYmVjb21pbmcgbW9yZSBzb3BoaXN0aWNhdGVkLCBuYXR1cmFsIGxhbmd1YWdlIHByb2Nlc3NpbmcgaXMgcmVhY2hpbmcgbmV3IGhlaWdodHMsIGFuZCBjb21wdXRlciB2aXNpb24gaXMgZW5hYmxpbmcgbWFjaGluZXMgdG8gc2VlIGFuZCB1bmRlcnN0YW5kIHRoZSB3b3JsZCBhcm91bmQgdGhlbSB3aXRoIHVucHJlY2VkZW50ZWQgYWNjdXJhY3kuXG5cbkluIHRoaXMgY29tcHJlaGVuc2l2ZSBleHBsb3JhdGlvbiwgd2UnbGwgZGl2ZSBkZWVwIGludG8gdGhlIGN1cnJlbnQgc3RhdGUgb2YgQUkgdGVjaG5vbG9neSBhbmQgZXhhbWluZSBob3cgaXQncyByZXNoYXBpbmcgdmFyaW91cyBhc3BlY3RzIG9mIG91ciBsaXZlcy4gV2UnbGwgYWxzbyBsb29rIGFoZWFkIHRvIHRoZSBmdXR1cmUgYW5kIGRpc2N1c3Mgd2hhdCB3ZSBjYW4gZXhwZWN0IGFzIEFJIGNvbnRpbnVlcyB0byBldm9sdmUgYW5kIG1hdHVyZS5gLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2Nzc0NDIxMzYwMTktMjE3ODBlY2FkOTk1P3c9ODAwJmg9NjAwJmZpdD1jcm9wJyxcbiAgICBhdXRob3I6IGF1dGhvcnNbMF0sXG4gICAgY2F0ZWdvcnk6IGNhdGVnb3JpZXNbMF0sXG4gICAgdGFnczogWydBSScsICdUZWNobm9sb2d5JywgJ0Z1dHVyZScsICdJbm5vdmF0aW9uJ10sXG4gICAgcHVibGlzaGVkQXQ6ICcyMDI0LTAxLTE1VDEwOjAwOjAwWicsXG4gICAgcmVhZFRpbWU6IDgsXG4gICAgdmlld3M6IDEyNTAsXG4gICAgbGlrZXM6IDg5LFxuICAgIGZlYXR1cmVkOiB0cnVlLFxuICAgIHNsdWc6ICdmdXR1cmUtb2YtYXJ0aWZpY2lhbC1pbnRlbGxpZ2VuY2UnXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIHRpdGxlOiAnMTAgSGlkZGVuIEdlbXMgaW4gU291dGhlYXN0IEFzaWEgWW91IE11c3QgVmlzaXQnLFxuICAgIGV4Y2VycHQ6ICdEaXNjb3ZlciBicmVhdGh0YWtpbmcgZGVzdGluYXRpb25zIG9mZiB0aGUgYmVhdGVuIHBhdGggdGhhdCB3aWxsIHRyYW5zZm9ybSB5b3VyIHRyYXZlbCBleHBlcmllbmNlLicsXG4gICAgY29udGVudDogYFNvdXRoZWFzdCBBc2lhIGlzIGEgdHJlYXN1cmUgdHJvdmUgb2YgaW5jcmVkaWJsZSBkZXN0aW5hdGlvbnMsIGJ1dCBiZXlvbmQgdGhlIHdlbGwta25vd24gdG91cmlzdCBob3RzcG90cyBsaWVzIGEgd29ybGQgb2YgaGlkZGVuIGdlbXMgd2FpdGluZyB0byBiZSBkaXNjb3ZlcmVkLiBUaGVzZSBsZXNzZXIta25vd24gbG9jYXRpb25zIG9mZmVyIGF1dGhlbnRpYyBjdWx0dXJhbCBleHBlcmllbmNlcywgc3R1bm5pbmcgbmF0dXJhbCBiZWF1dHksIGFuZCB0aGUgY2hhbmNlIHRvIGV4cGxvcmUgd2l0aG91dCB0aGUgY3Jvd2RzLlxuXG5Gcm9tIHNlY2x1ZGVkIGJlYWNoZXMgd2l0aCBjcnlzdGFsLWNsZWFyIHdhdGVycyB0byBhbmNpZW50IHRlbXBsZXMgbmVzdGxlZCBpbiBsdXNoIGp1bmdsZXMsIHRoZXNlIGhpZGRlbiBnZW1zIHByb3ZpZGUgYSBnbGltcHNlIGludG8gdGhlIHRydWUgaGVhcnQgb2YgU291dGhlYXN0IEFzaWEuIEVhY2ggZGVzdGluYXRpb24gb24gb3VyIGxpc3Qgb2ZmZXJzIHNvbWV0aGluZyB1bmlxdWUsIHdoZXRoZXIgaXQncyBpbmNyZWRpYmxlIGJpb2RpdmVyc2l0eSwgcmljaCBjdWx0dXJhbCBoZXJpdGFnZSwgb3Igc2ltcGx5IGJyZWF0aHRha2luZyBuYXR1cmFsIGJlYXV0eS5cblxuSm9pbiB1cyBhcyB3ZSBlbWJhcmsgb24gYSBqb3VybmV5IHRvIHVuY292ZXIgdGhlc2UgYW1hemluZyBwbGFjZXMgdGhhdCBkZXNlcnZlIGEgc3BvdCBvbiBldmVyeSB0cmF2ZWxlcidzIGJ1Y2tldCBsaXN0LmAsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNjkwNTkyNTM0Ni0yMWJkYTRkMzJkZjQ/dz04MDAmaD02MDAmZml0PWNyb3AnLFxuICAgIGF1dGhvcjogYXV0aG9yc1sxXSxcbiAgICBjYXRlZ29yeTogY2F0ZWdvcmllc1syXSxcbiAgICB0YWdzOiBbJ1RyYXZlbCcsICdTb3V0aGVhc3QgQXNpYScsICdIaWRkZW4gR2VtcycsICdBZHZlbnR1cmUnXSxcbiAgICBwdWJsaXNoZWRBdDogJzIwMjQtMDEtMTRUMTQ6MzA6MDBaJyxcbiAgICByZWFkVGltZTogMTIsXG4gICAgdmlld3M6IDIxMDAsXG4gICAgbGlrZXM6IDE1NixcbiAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICBzbHVnOiAnaGlkZGVuLWdlbXMtc291dGhlYXN0LWFzaWEnXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHRpdGxlOiAnU3VzdGFpbmFibGUgRmFzaGlvbjogQnVpbGRpbmcgYSBDb25zY2lvdXMgV2FyZHJvYmUnLFxuICAgIGV4Y2VycHQ6ICdMZWFybiBob3cgdG8gY3JlYXRlIGEgc3R5bGlzaCwgc3VzdGFpbmFibGUgd2FyZHJvYmUgdGhhdCByZWZsZWN0cyB5b3VyIHZhbHVlcyBhbmQgcmVkdWNlcyBlbnZpcm9ubWVudGFsIGltcGFjdC4nLFxuICAgIGNvbnRlbnQ6IGBUaGUgZmFzaGlvbiBpbmR1c3RyeSBpcyB1bmRlcmdvaW5nIGEgc2lnbmlmaWNhbnQgdHJhbnNmb3JtYXRpb24gYXMgY29uc3VtZXJzIGJlY29tZSBtb3JlIGNvbnNjaW91cyBhYm91dCB0aGUgZW52aXJvbm1lbnRhbCBhbmQgc29jaWFsIGltcGFjdCBvZiB0aGVpciBjbG90aGluZyBjaG9pY2VzLiBTdXN0YWluYWJsZSBmYXNoaW9uIGlzIG5vIGxvbmdlciBhIG5pY2hlIGNvbmNlcHQgYnV0IGEgZ3Jvd2luZyBtb3ZlbWVudCB0aGF0J3MgcmVzaGFwaW5nIGhvdyB3ZSB0aGluayBhYm91dCBzdHlsZSBhbmQgY29uc3VtcHRpb24uXG5cbkJ1aWxkaW5nIGEgY29uc2Npb3VzIHdhcmRyb2JlIGRvZXNuJ3QgbWVhbiBzYWNyaWZpY2luZyBzdHlsZSBvciBicmVha2luZyB0aGUgYmFuay4gSXQncyBhYm91dCBtYWtpbmcgdGhvdWdodGZ1bCBjaG9pY2VzIHRoYXQgYWxpZ24gd2l0aCB5b3VyIHZhbHVlcyB3aGlsZSBzdGlsbCBleHByZXNzaW5nIHlvdXIgcGVyc29uYWwgc3R5bGUuIEZyb20gY2hvb3NpbmcgcXVhbGl0eSBwaWVjZXMgdGhhdCBsYXN0IGxvbmdlciB0byBzdXBwb3J0aW5nIGJyYW5kcyB3aXRoIGV0aGljYWwgcHJhY3RpY2VzLCB0aGVyZSBhcmUgbWFueSB3YXlzIHRvIGVtYnJhY2Ugc3VzdGFpbmFibGUgZmFzaGlvbi5cblxuSW4gdGhpcyBndWlkZSwgd2UnbGwgZXhwbG9yZSBwcmFjdGljYWwgc3RyYXRlZ2llcyBmb3IgYnVpbGRpbmcgYSBzdXN0YWluYWJsZSB3YXJkcm9iZSwgaGlnaGxpZ2h0IGJyYW5kcyB0aGF0IGFyZSBsZWFkaW5nIHRoZSB3YXkgaW4gZXRoaWNhbCBmYXNoaW9uLCBhbmQgc2hhcmUgdGlwcyBmb3IgY2FyaW5nIGZvciB5b3VyIGNsb3RoZXMgdG8gZXh0ZW5kIHRoZWlyIGxpZmVzcGFuLmAsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ0NTIwNTE3MDIzMC0wNTNiODMwMTYwNTA/dz04MDAmaD02MDAmZml0PWNyb3AnLFxuICAgIGF1dGhvcjogYXV0aG9yc1syXSxcbiAgICBjYXRlZ29yeTogY2F0ZWdvcmllc1szXSxcbiAgICB0YWdzOiBbJ0Zhc2hpb24nLCAnU3VzdGFpbmFiaWxpdHknLCAnRXRoaWNhbCBGYXNoaW9uJywgJ1N0eWxlJ10sXG4gICAgcHVibGlzaGVkQXQ6ICcyMDI0LTAxLTEzVDA5OjE1OjAwWicsXG4gICAgcmVhZFRpbWU6IDYsXG4gICAgdmlld3M6IDg5MCxcbiAgICBsaWtlczogNjcsXG4gICAgZmVhdHVyZWQ6IGZhbHNlLFxuICAgIHNsdWc6ICdzdXN0YWluYWJsZS1mYXNoaW9uLWNvbnNjaW91cy13YXJkcm9iZSdcbiAgfVxuXVxuXG5leHBvcnQgY29uc3Qgc29jaWFsUmVlbHM6IFNvY2lhbFJlZWxbXSA9IFtcbiAge1xuICAgIGlkOiAnMScsXG4gICAgcGxhdGZvcm06ICdpbnN0YWdyYW0nLFxuICAgIHVybDogJ2h0dHBzOi8vd3d3Lmluc3RhZ3JhbS5jb20vcmVlbC9leGFtcGxlMScsXG4gICAgdGh1bWJuYWlsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MTExNjI2MTc0NzQtNWIyMWU4NzllMTEzP3c9NDAwJmg9NjAwJmZpdD1jcm9wJyxcbiAgICB0aXRsZTogJ1F1aWNrIFRlY2ggVGlwcyBmb3IgUHJvZHVjdGl2aXR5JyxcbiAgICBhdXRob3I6ICdAdGVjaGd1cnUnLFxuICAgIHZpZXdzOiAxNTQwMCxcbiAgICBsaWtlczogODkyLFxuICAgIGR1cmF0aW9uOiAzMFxuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICBwbGF0Zm9ybTogJ3Rpa3RvaycsXG4gICAgdXJsOiAnaHR0cHM6Ly93d3cudGlrdG9rLmNvbS9AZXhhbXBsZS92aWRlby8xMjMnLFxuICAgIHRodW1ibmFpbDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTE2MzIxMzE4NDIzLWYwNmY4NWU1MDRiMz93PTQwMCZoPTYwMCZmaXQ9Y3JvcCcsXG4gICAgdGl0bGU6ICdUcmF2ZWwgUGFja2luZyBIYWNrcycsXG4gICAgYXV0aG9yOiAnQHRyYXZlbGhhY2tzJyxcbiAgICB2aWV3czogMjg5MDAsXG4gICAgbGlrZXM6IDEyNTAsXG4gICAgZHVyYXRpb246IDQ1XG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHBsYXRmb3JtOiAneW91dHViZScsXG4gICAgdXJsOiAnaHR0cHM6Ly93d3cueW91dHViZS5jb20vc2hvcnRzL2V4YW1wbGUnLFxuICAgIHRodW1ibmFpbDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTYwNDcyMzU0LWIzM2ZmMGM0NGE0Mz93PTQwMCZoPTYwMCZmaXQ9Y3JvcCcsXG4gICAgdGl0bGU6ICdGYXNoaW9uIFN0eWxpbmcgVGlwcycsXG4gICAgYXV0aG9yOiAnQHN0eWxlcXVlZW4nLFxuICAgIHZpZXdzOiA4NzAwLFxuICAgIGxpa2VzOiA0NTYsXG4gICAgZHVyYXRpb246IDYwXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIHBsYXRmb3JtOiAnaW5zdGFncmFtJyxcbiAgICB1cmw6ICdodHRwczovL3d3dy5pbnN0YWdyYW0uY29tL3JlZWwvZXhhbXBsZTInLFxuICAgIHRodW1ibmFpbDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTU2OTA5MTE0LWY2ZTdhZDdkMzEzNj93PTQwMCZoPTYwMCZmaXQ9Y3JvcCcsXG4gICAgdGl0bGU6ICdIZWFsdGh5IFJlY2lwZSBpbiA2MCBTZWNvbmRzJyxcbiAgICBhdXRob3I6ICdAaGVhbHRoeWZvb2QnLFxuICAgIHZpZXdzOiAxMjMwMCxcbiAgICBsaWtlczogNjc4LFxuICAgIGR1cmF0aW9uOiA2MFxuICB9XG5dXG4iXSwibmFtZXMiOlsiYXV0aG9ycyIsImlkIiwibmFtZSIsImF2YXRhciIsImJpbyIsInNvY2lhbCIsInR3aXR0ZXIiLCJpbnN0YWdyYW0iLCJsaW5rZWRpbiIsImNhdGVnb3JpZXMiLCJzbHVnIiwiY29sb3IiLCJkZXNjcmlwdGlvbiIsInBvc3RzIiwidGl0bGUiLCJleGNlcnB0IiwiY29udGVudCIsImltYWdlIiwiYXV0aG9yIiwiY2F0ZWdvcnkiLCJ0YWdzIiwicHVibGlzaGVkQXQiLCJyZWFkVGltZSIsInZpZXdzIiwibGlrZXMiLCJmZWF0dXJlZCIsInNvY2lhbFJlZWxzIiwicGxhdGZvcm0iLCJ1cmwiLCJ0aHVtYm5haWwiLCJkdXJhdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/data/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8d1d9c66c2d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam5ld3MtYmxvZy8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MDFlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU4ZDFkOWM2NmMyZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"JNews Blog - Modern News & Magazine\",\n    description: \"A modern blog platform with social media integration and beautiful design\",\n    keywords: \"blog, news, magazine, social media, articles\",\n    authors: [\n        {\n            name: \"JNews Blog\"\n        }\n    ],\n    openGraph: {\n        title: \"JNews Blog - Modern News & Magazine\",\n        description: \"A modern blog platform with social media integration and beautiful design\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/kai-kal project/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/kai-kal project/src/components/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2Fkai-kal%20project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();