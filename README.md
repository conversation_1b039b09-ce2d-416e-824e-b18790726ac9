# JNews Blog - Modern News & Magazine Website

A modern, responsive blog website inspired by the JNews WordPress theme, built with Next.js, React, and Tailwind CSS.

## Features

- 🎨 **Modern Design**: Clean, professional layout inspired by JNews theme
- 📱 **Responsive**: Fully responsive design that works on all devices
- ⚡ **Fast Performance**: Built with Next.js for optimal performance
- 🎬 **Smooth Animations**: Beautiful animations using Framer Motion
- 📰 **Blog Functionality**: Complete blogging system with categories and tags
- 📱 **Social Media Integration**: Social reels display and sharing features
- 🔍 **Search Functionality**: Advanced search capabilities
- 📧 **Newsletter**: Email subscription system
- 🌙 **Dark Mode**: Toggle between light and dark themes
- 📊 **Analytics Ready**: Built-in support for analytics tracking

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Language**: TypeScript
- **Image Optimization**: Next.js Image component

## Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

3. **Open in Browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── about/          # About page
│   ├── contact/        # Contact page
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Homepage
├── components/         # Reusable components
│   ├── Header.tsx      # Navigation header
│   ├── Footer.tsx      # Site footer
│   ├── HeroSection.tsx # Homepage hero
│   ├── FeaturedPosts.tsx
│   ├── SocialReels.tsx
│   ├── LatestPosts.tsx
│   ├── TrendingTopics.tsx
│   └── Newsletter.tsx
├── data/               # Mock data and content
│   └── mockData.ts     # Sample posts and content
└── types/              # TypeScript type definitions
    └── index.ts        # Type definitions
```

## Key Components

### Header
- Responsive navigation with mobile menu
- Search functionality
- Dark mode toggle
- User authentication ready

### Hero Section
- Auto-rotating featured posts
- Smooth transitions and animations
- Category badges and meta information
- Call-to-action buttons

### Social Reels
- Instagram, TikTok, and YouTube integration
- Interactive reel viewer
- Platform-specific styling
- View counts and engagement metrics

### Blog Posts
- Featured and latest post sections
- Category filtering
- Author information
- Reading time estimates
- Social sharing buttons

## Customization

### Colors
Update the color scheme in `tailwind.config.ts`:

```typescript
colors: {
  primary: {
    // Your primary color palette
  },
  secondary: {
    // Your secondary color palette
  }
}
```

### Content
Modify the mock data in `src/data/mockData.ts` to add your own:
- Blog posts
- Authors
- Categories
- Social reels

### Styling
- Global styles: `src/app/globals.css`
- Component-specific styles: Tailwind classes in components
- Custom animations: Framer Motion configurations

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The project can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## Performance Optimizations

- **Image Optimization**: Next.js Image component with lazy loading
- **Code Splitting**: Automatic code splitting with Next.js
- **SEO Optimized**: Meta tags and structured data
- **Responsive Images**: Multiple image sizes for different devices
- **Lazy Loading**: Components and images load on demand

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For support and questions:
- Email: <EMAIL>
- GitHub Issues: Create an issue in this repository

---

Built with ❤️ using Next.js and Tailwind CSS
