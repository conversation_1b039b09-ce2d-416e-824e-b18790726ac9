#!/bin/bash

echo "Starting JNews Blog development server..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Dependencies not found. Installing..."
    if command -v npm &> /dev/null; then
        npm install
    elif command -v yarn &> /dev/null; then
        yarn install
    elif command -v pnpm &> /dev/null; then
        pnpm install
    else
        echo "No package manager found. Please install npm, yarn, or pnpm."
        exit 1
    fi
fi

# Start the development server
if command -v npm &> /dev/null; then
    npm run dev
elif command -v yarn &> /dev/null; then
    yarn dev
elif command -v pnpm &> /dev/null; then
    pnpm dev
else
    echo "No package manager found. Please install npm, yarn, or pnpm."
    exit 1
fi
