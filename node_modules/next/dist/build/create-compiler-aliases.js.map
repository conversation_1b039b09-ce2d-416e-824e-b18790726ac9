{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "names": ["createWebpackAliases", "createServerOnlyClientOnlyAliases", "createRSCAliases", "getOptimizedModuleAliases", "createServerComponentsNoopAliases", "distDir", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "PAGES_DIR_ALIAS", "reduce", "prev", "ext", "push", "path", "join", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST", "undefined", "hasExternalOtelApiPackage", "images", "loaderFile", "next", "defaultOverrides", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "DOT_NEXT_ALIAS", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "dirname", "setimmediate", "isServer", "bundledReactChannel", "layer", "alias", "react$", "WEBPACK_LAYERS", "serverSideRendering", "Object", "assign", "reactServerComponents", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;;;;IAyBgBA,oBAAoB;eAApBA;;IA4LAC,iCAAiC;eAAjCA;;IAsBAC,gBAAgB;eAAhBA;;IAkFAC,yBAAyB;eAAzBA;;IAyDAC,iCAAiC;eAAjCA;;;6DAtXC;2BAWV;6BAE0B;+BAK1B;;;;;;AAOA,SAASJ,qBAAqB,EACnCK,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAaZ;IACC,MAAMC,iBAAiBN,OAAOM,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIZ,KAAK;QACP,MAAMa,eAAe,eAAgBf,CAAAA,eAAe,SAAS,EAAC;QAC9Da,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,KAAK,CAAC,CAAC,GAAG;eACxCZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,KAAK,EAAEe,IAAI,CAAC;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,OAAO,CAAC,CAAC,GAAG;eAC1CZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,OAAO,EAAEe,IAAI,CAAC;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,CAAC,EAAEE,0BAAe,CAAC,UAAU,CAAC,CAAC,GAAG;eAClDZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,UAAU,EAAEe,IAAI,CAAC;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,cAAc;QAEd,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIf,eACA;YACE,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,sCAAsC;YACtC,CAACqB,aAAI,CAACC,IAAI,CAACC,gCAAiB,EAAE,UAAU,EACtC;YACF,CAACF,aAAI,CAACC,IAAI,CAACC,gCAAiB,EAAE,MAAM,EAClC;YACF,CAACF,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,UAAU,QAAQ,EACnD;YACF,CAACH,aAAI,CAACC,IAAI,CACRC,gCAAiB,EACjB,QACA,UACA,OACA,kBACA,EAAE;YACJ,CAACF,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,UAAU,UAAU,EACrD;YACF,CAACH,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,UAAU,UAAU,EACrD;YACF,CAACH,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,UAAU,OAAO,QAAQ,EAC1D;YACF,CAACH,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,UAAU,OAAO,WAAW,EAC7D;YACF,CAACH,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,SAAS,aAAa,EACvD;YACF,CAACH,aAAI,CAACC,IAAI,CAACE,qCAAsB,EAAE,SAAS,QAAQ,EAClD;YACF,CAACH,aAAI,CAACC,IAAI,CACRE,qCAAsB,EACtB,UACA,cACA,cACA,EAAE;YACJ,CAACH,aAAI,CAACC,IAAI,CACRE,qCAAsB,EACtB,UACA,cACA,WACA,EAAE;QACN,IACAC,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAACC,IAAAA,wCAAyB,OAAM;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIvB,OAAOwB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCzB,OAAOwB,MAAM,CAACC,UAAU;YAC7D,GAAI5B,gBAAgB;gBAClB,yCAAyCG,OAAOwB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAH,SAAS;QAEbI,MAAMN,gCAAiB;QAEvB,qBAAqBO,6BAAgB,CAAC,mBAAmB;QACzD,eAAeA,6BAAgB,CAAC,aAAa;QAE7C,GAAGjB,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIV,WAAW;YAAE,CAACY,0BAAe,CAAC,EAAEZ;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAAC0B,wBAAa,CAAC,EAAE1B;QAAO,IAAI,CAAC,CAAC;QAC7C,CAAC2B,yBAAc,CAAC,EAAE1B;QAClB,CAAC2B,yBAAc,CAAC,EAAEnC;QAClB,GAAIC,YAAYC,eAAeJ,8BAA8B,CAAC,CAAC;QAC/D,GAAIW,2BAA2B2B,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAIjC,eACAkC,6BACEhC,OAAOiC,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACC,oCAAyB,CAAC,EACzB;QAEF,CAACC,0CAA+B,CAAC,EAC/B;QAEF,CAACC,iCAAsB,CAAC,EACtB;QAEF,CAACC,sCAA2B,CAAC,EAC3B;QAEF,GAAI1C,YAAYC,eACZ;YACE,CAACU,sBAAsB,EAAEF,cACrBE,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkBW,aAAI,CAACC,IAAI,CACzBD,aAAI,CAACqB,OAAO,CAAC/B,QAAQC,OAAO,CAAC,+BAC7B;QAGF+B,cAAc;IAChB;AACF;AAEO,SAASjD,kCACdkD,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEO,SAASjD,iBACdkD,mBAA2B,EAC3B,EACEC,KAAK,EACL9C,YAAY,EACZO,wBAAwB,EAKzB;IAED,IAAIwC,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEH,oBAAoB,CAAC;QACxD,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,CAAC;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,gDAAgD,CAAC;QACvE,0BAA0B,CAAC,qDAAqD,CAAC;QACjF,6BAA6B,CAAC,wDAAwD,CAAC;QACvF,0BAA0B,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;QAC1F,6BAA6B,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;QAChG,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,+DAA+D;QAC/D,2DAA2D,CAAC,4CAA4C,CAAC;QACzG,wDAAwD,CAAC,4CAA4C,CAAC;IACxG;IAEA,IAAI,CAAC7C,cAAc;QACjB,IAAI8C,UAAUG,yBAAc,CAACC,mBAAmB,EAAE;YAChDH,QAAQI,OAAOC,MAAM,CAACL,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF,OAAO,IAAIA,UAAUG,yBAAc,CAACI,qBAAqB,EAAE;YACzDN,QAAQI,OAAOC,MAAM,CAACL,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;gBAChJ,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF;IACF;IAEA,IAAI9C,cAAc;QAChB,IAAI8C,UAAUG,yBAAc,CAACI,qBAAqB,EAAE;YAClDN,KAAK,CACH,SACD,GAAG,CAAC,wBAAwB,EAAEF,oBAAoB,oBAAoB,CAAC;QAC1E;QACA,4CAA4C;QAC5C,sDAAsD;QACtDE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,sBAAsB,CAAC;IAChF;IAEA,IAAItC,0BAA0B;QAC5BwC,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,UAAU,CAAC;IACpE;IAEAE,KAAK,CACH,gEACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAIO,SAASnD;IACd,OAAO;QACL0D,SAAS3C,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF2C,KAAK5C,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASuB,6BAA6BqB,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsBjD,QAAQ,CAAC,EAAEgD,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsBlD,QAAQC,OAAO,CAAC,CAAC,EAAE+C,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAGtC,aAAI,CAACC,IAAI,CAC5BD,aAAI,CAACqB,OAAO,CAACmB,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAASvB;IACP,OAAO;QACL,cAAc;IAChB;AACF;AACO,SAASrC;IACd,OAAO;QACL,CAACc,QAAQC,OAAO,CAAC,aAAa,EAAED,QAAQC,OAAO,CAC7C;QAEF,qBAAqB;QACrB,CAACD,QAAQC,OAAO,CAAC,gBAAgB,EAAED,QAAQC,OAAO,CAChD;IAEJ;AACF"}