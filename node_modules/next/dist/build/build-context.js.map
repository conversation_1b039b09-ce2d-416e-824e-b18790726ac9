{"version": 3, "sources": ["../../src/build/build-context.ts"], "names": ["resumePluginState", "getProxiedPluginState", "getPluginState", "NextBuildContext", "pluginState", "resumedState", "Object", "assign", "initialState", "Proxy", "get", "target", "key", "set", "value"], "mappings": ";;;;;;;;;;;;;;;;;IAcgBA,iBAAiB;eAAjBA;;IAOAC,qBAAqB;eAArBA;;IAiBAC,cAAc;eAAdA;;IAQHC,gBAAgB;eAAhBA;;;AArCb,4EAA4E;AAC5E,4EAA4E;AAC5E,+DAA+D;AAC/D,+CAA+C;AAC/C,IAAIC,cAAmC,CAAC;AACjC,SAASJ,kBAAkBK,YAAkC;IAClEC,OAAOC,MAAM,CAACH,aAAaC;AAC7B;AAKO,SAASJ,sBACdO,YAAmB;IAEnB,OAAO,IAAIC,MAAML,aAAa;QAC5BM,KAAIC,MAAM,EAAEC,GAAW;YACrB,IAAI,OAAOD,MAAM,CAACC,IAAI,KAAK,aAAa;gBACtC,OAAQD,MAAM,CAACC,IAAI,GAAGJ,YAAY,CAACI,IAAI;YACzC;YACA,OAAOD,MAAM,CAACC,IAAI;QACpB;QACAC,KAAIF,MAAM,EAAEC,GAAW,EAAEE,KAAK;YAC5BH,MAAM,CAACC,IAAI,GAAGE;YACd,OAAO;QACT;IACF;AACF;AAEO,SAASZ;IACd,OAAOE;AACT;AAMO,MAAMD,mBAoDR,CAAC"}