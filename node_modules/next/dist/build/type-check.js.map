{"version": 3, "sources": ["../../src/build/type-check.ts"], "names": ["startTypeChecking", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "JestWorker", "require", "resolve", "numWorkers", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "then", "result", "end", "catch", "exit", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "path", "join", "Log", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "createSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "verifyAndLint", "eslint", "dirs", "stopAndPersist", "record", "eventTypeCheckCompleted", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "isError", "message", "flush"], "mappings": ";;;;+BAqEsBA;;;eAAAA;;;6DAjEL;6DACI;4BACgB;+BACP;gEACJ;wBACc;gEACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIC,kBAAU,CACpCC,QAAQC,OAAO,CAAC,mCAChB;QACEC,YAAY;QACZP;QACAQ,YAAY;IACd;IAKFL,gBAAgBM,SAAS,GAAGC,IAAI,CAACC,QAAQC,MAAM;IAC/CT,gBAAgBU,SAAS,GAAGH,IAAI,CAACC,QAAQG,MAAM;IAE/C,OAAOX,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCa,IAAI,CAAC,CAACC;QACLb,gBAAgBc,GAAG;QACnB,OAAOD;IACT,GACCE,KAAK,CAAC;QACL,2FAA2F;QAC3F,8FAA8F;QAC9FP,QAAQQ,IAAI,CAAC;IACf;AACJ;AAEO,eAAe5B,kBAAkB,EACtCQ,QAAQ,EACRqB,MAAM,EACN3B,GAAG,EACH4B,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiBC,aAAI,CAACC,IAAI,CAACnC,UAAU;IAE3C,IAAI6B,wBAAwB;QAC1BO,KAAIC,IAAI,CAAC;IACX;IACA,IAAIZ,WAAWH,cAAc;QAC3B,uEAAuE;QACvEc,KAAIC,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACV,0BAA0BH,YAAY;QACzCY,0CACE;IACJ,OAAO,IAAI,CAACT,wBAAwB;QAClCS,0CAA0C;IAC5C,OAAO,IAAIZ,YAAY;QACrBY,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgCC,IAAAA,gBAAa,EAC3CF;IAEJ;IAEA,MAAMG,iBAAiB7B,QAAQ8B,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDvB,cAAcwB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DvD,sBACEC,KACA2B,OAAO1B,OAAO,EACd;oBAAC6B;oBAAUI;iBAAO,CAACqB,MAAM,CAACnB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAACjC,YAAY,EAC9BuB,OAAO6B,MAAM,CAACnD,mBAAmB,EACjCC,UACAqB,OAAO8B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACxB,QACF,CAAC,CAACJ,UACFR,IAAI,CAAC,CAACqC;oBACN,MAAMC,WAAW1C,QAAQ8B,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEF5B,cACEH,cAAcwB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrD3B;gBAHF,MAAMkC,IAAAA,4BAAa,EACjB7D,KACAuC,iBACAZ,iBAAAA,OAAOmC,MAAM,qBAAbnC,eAAeoC,IAAI,EACnBpC,OAAO8B,YAAY,CAACC,aAAa,EACjCzB;YAEJ;SACH;QACDY,iDAAAA,8BAA+BmB,cAAc;QAE7C,IAAI,CAAC7B,0BAA0Bc,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBhB,UAAUgC,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtBC,mBAAmBjB,YAAY,CAAC,EAAE;gBAClCkB,mBAAmBnB,aAAaoB,OAAO;gBACvCC,eAAe,GAAErB,uBAAAA,aAAa1B,MAAM,qBAAnB0B,qBAAqBqB,eAAe;gBACrDC,eAAe,GAAEtB,wBAAAA,aAAa1B,MAAM,qBAAnB0B,sBAAqBsB,eAAe;gBACrDC,WAAW,GAAEvB,wBAAAA,aAAa1B,MAAM,qBAAnB0B,sBAAqBuB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,OAAO,KAAK,8BAA8B;YAChE,MAAM1C,UAAU2C,KAAK;YACrB1D,QAAQQ,IAAI,CAAC;QACf;QACA,MAAM+C;IACR;AACF"}