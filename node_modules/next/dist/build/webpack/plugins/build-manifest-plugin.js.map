{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "names": ["srcEmptySsgManifest", "normalizeRewritesForBuildManifest", "getEntrypointFiles", "BuildManifestPlugin", "normalizeRewrite", "item", "has", "source", "destination", "rewrites", "afterFiles", "map", "beforeFiles", "fallback", "generateClientManifest", "compiler", "compilation", "assetMap", "compilationSpan", "spans", "get", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "appDependencies", "Set", "pages", "sortedPageKeys", "getSortedRoutes", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "devalue", "entrypoint", "getFiles", "file", "test", "replace", "processRoute", "r", "rewrite", "startsWith", "constructor", "options", "buildId", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "exportRuntime", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "lowPriorityFiles", "rootMainFiles", "ampFirstPages", "ampFirstEntryNames", "ampFirstEntryNamesMap", "entryName", "pagePath", "getRouteFromEntrypoint", "push", "mainFiles", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "compilationAssets", "getAssets", "p", "name", "endsWith", "info", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "v", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "values", "SYSTEM_ENTRYPOINTS", "filesForPage", "CLIENT_STATIC_FILES_PATH", "ssgManifestPath", "sources", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "BUILD_MANIFEST", "JSON", "stringify", "MIDDLEWARE_BUILD_MANIFEST", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;IA6BaA,mBAAmB;eAAnBA;;IAcGC,iCAAiC;eAAjCA;;IAqDAC,kBAAkB;eAAlBA;;IAuBhB,iFAAiF;IACjF,+GAA+G;IAC/G,OAiLC;eAjLoBC;;;gEAxHD;yBACa;2BAW1B;+EAE4B;0CACG;uBACN;iCACV;;;;;;AAWf,MAAMH,sBAAsB,CAAC,4EAA4E,CAAC;AAEjH,SAASI,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEO,SAASP,kCACdQ,QAAkC;QAGpBA,sBACCA,uBACHA;IAHZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,qBAAnBD,qBAAqBE,GAAG,CAAC,CAACN,OAASD,iBAAiBC;QAChEO,WAAW,GAAEH,wBAAAA,SAASG,WAAW,qBAApBH,sBAAsBE,GAAG,CAAC,CAACN,OAASD,iBAAiBC;QAClEQ,QAAQ,GAAEJ,qBAAAA,SAASI,QAAQ,qBAAjBJ,mBAAmBE,GAAG,CAAC,CAACN,OAASD,iBAAiBC;IAC9D;AACF;AAEA,mFAAmF;AACnF,yCAAyC;AACzC,SAASS,uBACPC,QAAa,EACbC,WAAgB,EAChBC,QAAuB,EACvBR,QAAkC;IAElC,MAAMS,kBAAkBC,sBAAK,CAACC,GAAG,CAACJ,gBAAgBG,sBAAK,CAACC,GAAG,CAACL;IAC5D,MAAMM,wBAAwBH,mCAAAA,gBAAiBI,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYxB,kCAAkCQ;QAChD;QACA,MAAMiB,kBAAkB,IAAIC,IAAIV,SAASW,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiBC,IAAAA,sBAAe,EAACC,OAAOC,IAAI,CAACf,SAASW,KAAK;QAEjEC,eAAeI,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAelB,SAASW,KAAK,CAACM,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACZ,gBAAgBpB,GAAG,CAACgC;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBf,cAAc,CAACU,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEZ,eAAegB,WAAW,GAAGX;QAE7B,OAAOY,IAAAA,gBAAO,EAACjB;IACjB;AACF;AAEO,SAAStB,mBAAmBwC,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACCjC,GAAG,CAAC,CAACiC,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,MAAMC,eAAe,CAACC;IACpB,MAAMC,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,CAACC,QAAQzC,WAAW,CAAC0C,UAAU,CAAC,MAAM;QACxC,OAAO,AAACD,QAAgBzC,WAAW;IACrC;IACA,OAAOyC;AACT;AAIe,MAAM9C;IAOnBgD,YAAYC,OAMX,CAAE;QACD,IAAI,CAACC,OAAO,GAAGD,QAAQC,OAAO;QAC9B,IAAI,CAACC,aAAa,GAAG,CAAC,CAACF,QAAQE,aAAa;QAC5C,IAAI,CAAC7C,QAAQ,GAAG;YACdG,aAAa,EAAE;YACfF,YAAY,EAAE;YACdG,UAAU,EAAE;QACd;QACA,IAAI,CAAC0C,aAAa,GAAGH,QAAQG,aAAa;QAC1C,IAAI,CAAC9C,QAAQ,CAACG,WAAW,GAAGwC,QAAQ3C,QAAQ,CAACG,WAAW,CAACD,GAAG,CAACoC;QAC7D,IAAI,CAACtC,QAAQ,CAACC,UAAU,GAAG0C,QAAQ3C,QAAQ,CAACC,UAAU,CAACC,GAAG,CAACoC;QAC3D,IAAI,CAACtC,QAAQ,CAACI,QAAQ,GAAGuC,QAAQ3C,QAAQ,CAACI,QAAQ,CAACF,GAAG,CAACoC;QACvD,IAAI,CAACS,aAAa,GAAG,CAAC,CAACJ,QAAQI,aAAa;IAC9C;IAEAC,aAAa1C,QAAa,EAAEC,WAAgB,EAAE0C,MAAW,EAAE;QACzD,MAAMxC,kBAAkBC,sBAAK,CAACC,GAAG,CAACJ,gBAAgBG,sBAAK,CAACC,GAAG,CAACL;QAC5D,MAAM4C,mBAAmBzC,mCAAAA,gBAAiBI,UAAU,CAClD;QAEF,OAAOqC,oCAAAA,iBAAkBpC,OAAO,CAAC;YAC/B,MAAMqC,cAAgC5C,YAAY4C,WAAW;YAC7D,MAAM3C,WAAuC;gBAC3C4C,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACfC,kBAAkB,EAAE;gBACpBC,eAAe,EAAE;gBACjBrC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBsC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBC,+CAAqB,CAAChD,GAAG,CAACJ;YACrD,IAAImD,oBAAoB;gBACtB,KAAK,MAAME,aAAaF,mBAAoB;oBAC1C,MAAMG,WAAWC,IAAAA,+BAAsB,EAACF;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEArD,SAASiD,aAAa,CAACM,IAAI,CAACF;gBAC9B;YACF;YAEA,MAAMG,YAAY,IAAI9C,IACpBzB,mBAAmB0D,YAAYxC,GAAG,CAACsD,2CAAgC;YAGrE,IAAI,IAAI,CAACnB,aAAa,EAAE;gBACtBtC,SAASgD,aAAa,GAAG;uBACpB,IAAItC,IACLzB,mBACE0D,YAAYxC,GAAG,CAACuD,+CAAoC;iBAGzD;YACH;YAEA,MAAMC,oBAIA5D,YAAY6D,SAAS;YAE3B5D,SAAS4C,aAAa,GAAGe,kBACtBvC,MAAM,CAAC,CAACyC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAEC,IAAI,CAACC,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACEF,EAAEG,IAAI,IAAIC,uDAA4C,IAAIJ,EAAEG,IAAI;YAEpE,GACCtE,GAAG,CAAC,CAACwE,IAAMA,EAAEJ,IAAI;YAEpB9D,SAAS6C,QAAQ,GAAG5D,mBAClB0D,YAAYxC,GAAG,CAACgE,oDAAyC,GACzD/C,MAAM,CAAC,CAACO,OAAS,CAAC6B,UAAUnE,GAAG,CAACsC;YAElC3B,SAAS8C,WAAW,GAAG7D,mBACrB0D,YAAYxC,GAAG,CAACiE,0CAA+B;YAGjD,KAAK,MAAM3C,cAAc1B,YAAY4C,WAAW,CAAC0B,MAAM,GAAI;gBACzD,IAAIC,6BAAkB,CAACjF,GAAG,CAACoC,WAAWqC,IAAI,GAAG;gBAC7C,MAAMT,WAAWC,IAAAA,+BAAsB,EAAC7B,WAAWqC,IAAI;gBAEvD,IAAI,CAACT,UAAU;oBACb;gBACF;gBAEA,MAAMkB,eAAetF,mBAAmBwC;gBAExCzB,SAASW,KAAK,CAAC0C,SAAS,GAAG;uBAAI,IAAI3C,IAAI;2BAAI8C;2BAAce;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAAClC,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5BrC,SAAS+C,gBAAgB,CAACQ,IAAI,CAC5B,CAAC,EAAEiB,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAAC,kBAAkB,CAAC;gBAEjE,MAAMqC,kBAAkB,CAAC,EAAED,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAAC,gBAAgB,CAAC;gBAErFpC,SAAS+C,gBAAgB,CAACQ,IAAI,CAACkB;gBAC/BhC,MAAM,CAACgC,gBAAgB,GAAG,IAAIC,gBAAO,CAACC,SAAS,CAAC5F;YAClD;YAEAiB,SAASW,KAAK,GAAGG,OAAOC,IAAI,CAACf,SAASW,KAAK,EACxCiE,IAAI,EACL,2BAA2B;aAC1BC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAG/E,SAASW,KAAK,CAACoE,EAAE,EAAGD,CAAAA,GAAI,CAAC;YAEtD,IAAIE,oBAAoBC,yBAAc;YAEtC,IAAI,IAAI,CAAC5C,aAAa,EAAE;gBACtB2C,oBAAoB,CAAC,SAAS,EAAEC,yBAAc,CAAC,CAAC;YAClD;YAEAxC,MAAM,CAACuC,kBAAkB,GAAG,IAAIN,gBAAO,CAACC,SAAS,CAC/CO,KAAKC,SAAS,CAACnF,UAAU,MAAM;YAGjC,IAAI,IAAI,CAACuC,aAAa,EAAE;gBACtBE,MAAM,CAAC,CAAC,OAAO,EAAE2C,oCAAyB,CAAC,GAAG,CAAC,CAAC,GAC9C,IAAIV,gBAAO,CAACC,SAAS,CACnB,CAAC,sBAAsB,EAAEO,KAAKC,SAAS,CAACnF,UAAU,CAAC;YAEzD;YAEA,IAAI,CAAC,IAAI,CAACqC,aAAa,EAAE;gBACvB,MAAMgD,qBAAqB,CAAC,EAAEb,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAAC,kBAAkB,CAAC;gBAE1FK,MAAM,CAAC4C,mBAAmB,GAAG,IAAIX,gBAAO,CAACC,SAAS,CAChD,CAAC,wBAAwB,EAAE9E,uBACzBC,UACAC,aACAC,UACA,IAAI,CAACR,QAAQ,EACb,uDAAuD,CAAC;YAE9D;YAEA,OAAOiD;QACT;IACF;IAEA6C,MAAMxF,QAA0B,EAAE;QAChCA,SAASyF,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAAC1F;YAC9CA,YAAYwF,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACE3B,MAAM;gBACN6B,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACrD;gBACC,IAAI,CAACD,YAAY,CAAC1C,UAAUC,aAAa0C;YAC3C;QAEJ;QACA;IACF;AACF"}