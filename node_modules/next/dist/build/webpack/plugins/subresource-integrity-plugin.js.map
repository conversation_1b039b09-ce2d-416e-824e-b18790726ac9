{"version": 3, "sources": ["../../../../src/build/webpack/plugins/subresource-integrity-plugin.ts"], "names": ["SubresourceIntegrityPlugin", "PLUGIN_NAME", "constructor", "algorithm", "apply", "compiler", "hooks", "make", "tap", "compilation", "afterOptimizeAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "assets", "files", "Set", "asset", "getAssets", "add", "hashes", "file", "values", "Error", "buffer", "hash", "crypto", "createHash", "update", "digest", "toString", "json", "JSON", "stringify", "SUBRESOURCE_INTEGRITY_MANIFEST", "sources", "RawSource"], "mappings": ";;;;+BAQaA;;;eAAAA;;;yBARoB;+DACd;2BAC4B;;;;;;AAE/C,MAAMC,cAAc;AAIb,MAAMD;IACXE,YAA6BC,UAA0C;yBAA1CA;IAA2C;IAEjEC,MAAMC,QAA0B,EAAE;QACvCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACP,aAAa,CAACQ;YACpCA,YAAYH,KAAK,CAACI,mBAAmB,CAACF,GAAG,CACvC;gBACEG,MAAMV;gBACNW,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC;gBACC,0BAA0B;gBAC1B,IAAIC,QAAQ,IAAIC;gBAChB,KAAK,MAAMC,SAASV,YAAYW,SAAS,GAAI;oBAC3CH,MAAMI,GAAG,CAACF,MAAMR,IAAI;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMW,SAAiC,CAAC;gBACxC,KAAK,MAAMC,QAAQN,MAAMO,MAAM,GAAI;oBACjC,gCAAgC;oBAChC,MAAML,QAAQH,MAAM,CAACO,KAAK;oBAC1B,IAAI,CAACJ,OAAO;wBACV,MAAM,IAAIM,MAAM,CAAC,qBAAqB,EAAEF,KAAK,CAAC;oBAChD;oBAEA,gCAAgC;oBAChC,MAAMG,SAASP,MAAMO,MAAM;oBAE3B,mCAAmC;oBACnC,MAAMC,OAAOC,eAAM,CAChBC,UAAU,CAAC,IAAI,CAAC1B,SAAS,EACzB2B,MAAM,CAACJ,QACPK,MAAM,GACNC,QAAQ,CAAC;oBAEZV,MAAM,CAACC,KAAK,GAAG,CAAC,EAAE,IAAI,CAACpB,SAAS,CAAC,CAAC,EAAEwB,KAAK,CAAC;gBAC5C;gBAEA,MAAMM,OAAOC,KAAKC,SAAS,CAACb,QAAQ,MAAM;gBAC1C,MAAMC,OAAO,YAAYa,yCAA8B;gBACvDpB,MAAM,CAACO,OAAO,MAAM,GAAG,IAAIc,gBAAO,CAACC,SAAS,CAC1C,CAAC,sCAAsC,EAAEJ,KAAKC,SAAS,CAACF,MAAM,CAAC;gBAIjEjB,MAAM,CAACO,OAAO,QAAQ,GAAG,IAAIc,gBAAO,CAACC,SAAS,CAC5CL;YAIJ;QAEJ;IACF;AACF"}