{"version": 3, "sources": ["../../../../src/build/webpack/plugins/pages-manifest-plugin.ts"], "names": ["edgeServerPages", "nodeServerPages", "edgeServerAppPaths", "nodeServerAppPaths", "PagesManifestPlugin", "constructor", "dev", "distDir", "isEdgeRuntime", "appDirEnabled", "createAssets", "compilation", "assets", "entrypoints", "pages", "appPaths", "entrypoint", "values", "pagePath", "getRouteFromEntrypoint", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "length", "slice", "normalizePathSep", "startsWith", "writeMergedManifest", "manifestPath", "entries", "fs", "mkdir", "path", "dirname", "recursive", "writeFile", "JSON", "stringify", "readFile", "then", "res", "parse", "catch", "pagesManifestPath", "join", "PAGES_MANIFEST", "sources", "RawSource", "appPathsManifestPath", "APP_PATHS_MANIFEST", "apply", "compiler", "hooks", "make", "tap", "processAssets", "tapPromise", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;;IAYWA,eAAe;eAAfA;;IACAC,eAAe;eAAfA;;IACAC,kBAAkB;eAAlBA;;IACAC,kBAAkB;eAAlBA;;IAEX,mEAAmE;IACnE,2GAA2G;IAC3G,0DAA0D;IAC1D,OAsKC;eAtKoBC;;;6DApBJ;iEACF;yBACkB;2BAI1B;+EAC4B;kCACF;;;;;;AAI1B,IAAIJ,kBAAkB,CAAC;AACvB,IAAIC,kBAAkB,CAAC;AACvB,IAAIC,qBAAqB,CAAC;AAC1B,IAAIC,qBAAqB,CAAC;AAKlB,MAAMC;IAQnBC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,aAAa,EACbC,aAAa,EAMd,CAAE;QACD,IAAI,CAACH,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEA,MAAMC,aAAaC,WAAgB,EAAEC,MAAW,EAAE;QAChD,MAAMC,cAAcF,YAAYE,WAAW;QAC3C,MAAMC,QAAuB,CAAC;QAC9B,MAAMC,WAA0B,CAAC;QAEjC,KAAK,MAAMC,cAAcH,YAAYI,MAAM,GAAI;YAC7C,MAAMC,WAAWC,IAAAA,+BAAsB,EACrCH,WAAWI,IAAI,EACf,IAAI,CAACX,aAAa;YAGpB,IAAI,CAACS,UAAU;gBACb;YACF;YAEA,MAAMG,QAAQL,WACXM,QAAQ,GACRC,MAAM,CACL,CAACC,OACC,CAACA,KAAKC,QAAQ,CAAC,sBACf,CAACD,KAAKC,QAAQ,CAAC,0BACfD,KAAKE,QAAQ,CAAC;YAGpB,+BAA+B;YAC/B,IAAI,CAACL,MAAMM,MAAM,EAAE;gBACjB;YACF;YACA,mHAAmH;YACnH,IAAIH,OAAOH,KAAK,CAACA,MAAMM,MAAM,GAAG,EAAE;YAElC,IAAI,CAAC,IAAI,CAACrB,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;oBACvBgB,OAAOA,KAAKI,KAAK,CAAC;gBACpB;YACF;YACAJ,OAAOK,IAAAA,kCAAgB,EAACL;YAExB,IAAIR,WAAWI,IAAI,CAACU,UAAU,CAAC,SAAS;gBACtCf,QAAQ,CAACG,SAAS,GAAGM;YACvB,OAAO;gBACLV,KAAK,CAACI,SAAS,GAAGM;YACpB;QACF;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,IAAI,CAAChB,aAAa,EAAE;YACtBR,kBAAkBc;YAClBZ,qBAAqBa;QACvB,OAAO;YACLd,kBAAkBa;YAClBX,qBAAqBY;QACvB;QAEA,gDAAgD;QAChD,sDAAsD;QACtD,MAAMgB,sBAAsB,OAC1BC,cACAC;YAEA,MAAMC,iBAAE,CAACC,KAAK,CAACC,aAAI,CAACC,OAAO,CAACL,eAAe;gBAAEM,WAAW;YAAK;YAC7D,MAAMJ,iBAAE,CAACK,SAAS,CAChBP,cACAQ,KAAKC,SAAS,CACZ;gBACE,GAAI,MAAMP,iBAAE,CACTQ,QAAQ,CAACV,cAAc,QACvBW,IAAI,CAAC,CAACC,MAAQJ,KAAKK,KAAK,CAACD,MACzBE,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA,EAAG;gBACpB,GAAGb,OAAO;YACZ,GACA,MACA;QAGN;QAEA,IAAI,IAAI,CAAC1B,OAAO,EAAE;YAChB,MAAMwC,oBAAoBX,aAAI,CAACY,IAAI,CACjC,IAAI,CAACzC,OAAO,EACZ,UACA0C,yBAAc;YAEhB,MAAMlB,oBAAoBgB,mBAAmB;gBAC3C,GAAG/C,eAAe;gBAClB,GAAGC,eAAe;YACpB;QACF,OAAO;YACLW,MAAM,CAAC,AAAC,CAAA,CAAC,IAAI,CAACN,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKyC,yBAAc,CAAC,GACtE,IAAIC,gBAAO,CAACC,SAAS,CACnBX,KAAKC,SAAS,CACZ;gBACE,GAAGzC,eAAe;gBAClB,GAAGC,eAAe;YACpB,GACA,MACA;QAGR;QAEA,IAAI,IAAI,CAACQ,aAAa,EAAE;YACtB,IAAI,IAAI,CAACF,OAAO,EAAE;gBAChB,MAAM6C,uBAAuBhB,aAAI,CAACY,IAAI,CACpC,IAAI,CAACzC,OAAO,EACZ,UACA8C,6BAAkB;gBAEpB,MAAMtB,oBAAoBqB,sBAAsB;oBAC9C,GAAGlD,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB;YACF,OAAO;gBACLS,MAAM,CACJ,AAAC,CAAA,CAAC,IAAI,CAACN,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAK6C,6BAAkB,CACrE,GAAG,IAAIH,gBAAO,CAACC,SAAS,CACvBX,KAAKC,SAAS,CACZ;oBACE,GAAGvC,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB,GACA,MACA;YAGN;QACF;IACF;IAEAmD,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAAC/C;YAC9CA,YAAY6C,KAAK,CAACG,aAAa,CAACC,UAAU,CACxC;gBACExC,MAAM;gBACNyC,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACpD,SAAW,IAAI,CAACF,YAAY,CAACC,aAAaC;QAE/C;IACF;AACF"}