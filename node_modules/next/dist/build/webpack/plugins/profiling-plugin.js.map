{"version": 3, "sources": ["../../../../src/build/webpack/plugins/profiling-plugin.ts"], "names": ["spans", "webpackInvalidSpans", "Profiling<PERSON><PERSON><PERSON>", "pluginName", "WeakMap", "moduleSpansByCompilation", "makeSpanByCompilation", "sealSpanByCompilation", "TRACE_LABELS_SEAL", "inTraceLabelsSeal", "label", "some", "l", "startsWith", "constructor", "runWebpackSpan", "apply", "compiler", "traceTopLevelHooks", "traceCompilationHooks", "traceHookPair", "spanName", "startHook", "stopHook", "parentSpan", "attrs", "onStart", "onStop", "span", "tap", "name", "stage", "Infinity", "params", "attributes", "<PERSON><PERSON><PERSON><PERSON>", "stop", "hooks", "compilation", "afterCompile", "get", "set", "options", "mode", "invalid", "done", "delete", "fileName", "trigger", "emit", "afterEmit", "make", "finishMake", "compilationSpan", "_span", "buildModule", "module", "moduleType", "userRequest", "split", "pop", "issuerModule", "moduleGraph", "get<PERSON><PERSON><PERSON>", "moduleSpans", "issuerSpan", "incomingConnection", "getIncomingConnections", "entrySpan", "dependency", "setAttribute", "layer", "moduleHooks", "NormalModule", "getCompilationHooks", "readResource", "for", "undefined", "intercept", "register", "tapInfo", "fn", "loaderContext", "callback", "moduleSpan", "currentTraceSpan", "err", "result", "loader", "succeedModule", "failedModule", "seal", "afterSeal", "addEntry", "entry", "addEntrySpan", "request", "<PERSON><PERSON><PERSON><PERSON>", "failedEntry", "beforeChunks", "after<PERSON><PERSON><PERSON>", "optimize", "reviveModules", "optimizeModules", "afterOptimizeModules", "optimizeChunks", "afterOptimizeChunks", "optimizeTree", "afterOptimizeTree", "optimizeChunkModules", "afterOptimizeChunkModules", "beforeModuleHash", "afterModuleHash", "beforeCodeGeneration", "afterCodeGeneration", "beforeHash", "afterHash", "beforeModuleAssets", "logs", "Map", "originalTime", "logger", "time", "originalTimeEnd", "timeEnd", "call", "replace"], "mappings": ";;;;;;;;;;;;;;;;IAKaA,KAAK;eAALA;;IAOAC,mBAAmB;eAAnBA;;IAcAC,eAAe;eAAfA;;;yBA1BgB;AAI7B,MAAMC,aAAa;AACZ,MAAMH,QAAQ,IAAII;AACzB,MAAMC,2BAA2B,IAAID;AAIrC,MAAME,wBAAwB,IAAIF;AAClC,MAAMG,wBAAwB,IAAIH;AAC3B,MAAMH,sBAAsB,IAAIG;AAEvC,MAAMI,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,kBAAkBC,KAAa;IACtC,OAAOF,kBAAkBG,IAAI,CAAC,CAACC,IAAMF,MAAMG,UAAU,CAACD;AACxD;AAEO,MAAMV;IAIXY,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IACAC,MAAMC,QAAa,EAAE;QACnB,IAAI,CAACC,kBAAkB,CAACD;QACxB,IAAI,CAACE,qBAAqB,CAACF;QAC3B,IAAI,CAACA,QAAQ,GAAGA;IAClB;IAEAG,cACEC,QAAiC,EACjCC,SAAc,EACdC,QAAa,EACb,EACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EAMP,GAAG,CAAC,CAAC,EACN;QACA,IAAIC;QACJN,UAAUO,GAAG,CACX;YAAEC,MAAM3B;YAAY4B,OAAO,CAACC;QAAS,GACrC,CAAC,GAAGC;YACF,MAAMH,OAAO,OAAOT,aAAa,aAAaA,aAAaA;YAC3D,MAAMa,aAAaT,QAAQA,SAASQ,UAAUR;YAC9CG,OAAOJ,aACHA,cAAcS,QAAQE,UAAU,CAACL,MAAMI,cACvC,IAAI,CAACnB,cAAc,CAACoB,UAAU,CAACL,MAAMI;YAEzC,IAAIR,SAASA,QAAQE,SAASK;QAChC;QAEFV,SAASM,GAAG,CAAC;YAAEC,MAAM3B;YAAY4B,OAAOC;QAAS,GAAG,CAAC,GAAGC;YACtD,gEAAgE;YAChE,6DAA6D;YAC7D,0BAA0B;YAC1B,IAAI,CAACL,MAAM;gBACT;YACF;YAEA,IAAID,QAAQA,OAAOC,SAASK;YAC5BL,KAAKQ,IAAI;QACX;IACF;IAEAlB,mBAAmBD,QAAa,EAAE;QAChC,IAAI,CAACG,aAAa,CAChB,uBACAH,SAASoB,KAAK,CAACC,WAAW,EAC1BrB,SAASoB,KAAK,CAACE,YAAY,EAC3B;YACEf,YAAY,IACVvB,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;YAC1DU,OAAO,IAAO,CAAA;oBAAEK,MAAMb,SAASa,IAAI;gBAAC,CAAA;YACpCJ,SAAS,CAACE,MAAMU;gBACdtC,MAAMyC,GAAG,CAACH,aAAaV;gBACvB5B,MAAMyC,GAAG,CAACxB,UAAUW;gBACpBvB,yBAAyBoC,GAAG,CAACH,aAAa,IAAIlC;YAChD;QACF;QAGF,IAAIa,SAASyB,OAAO,CAACC,IAAI,KAAK,eAAe;YAC3C,IAAI,CAACvB,aAAa,CAChB,IAAM,CAAC,oBAAoB,EAAEH,SAASa,IAAI,CAAC,CAAC,EAC5Cb,SAASoB,KAAK,CAACO,OAAO,EACtB3B,SAASoB,KAAK,CAACQ,IAAI,EACnB;gBACEnB,SAAS,CAACE,OAAS3B,oBAAoBwC,GAAG,CAACxB,UAAUW;gBACrDD,QAAQ,IAAM1B,oBAAoB6C,MAAM,CAAC7B;gBACzCQ,OAAO,CAACsB,WAAmB,CAAA;wBACzBC,SAASD,YAAY;oBACvB,CAAA;YACF;QAEJ;IACF;IAEA5B,sBAAsBF,QAAa,EAAE;QACnC,IAAI,CAACG,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACY,IAAI,EAAEhC,SAASoB,KAAK,CAACa,SAAS,EAAE;YACxE1B,YAAY,IACVvB,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;QAC5D;QAEA,IAAI,CAACK,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACc,IAAI,EAAElC,SAASoB,KAAK,CAACe,UAAU,EAAE;YACzE5B,YAAY,CAACc;gBACX,MAAMe,kBAAkBrD,MAAMwC,GAAG,CAACF;gBAClC,IAAI,CAACe,iBAAiB;oBACpB,OAAOpD,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;gBACjE;gBAEA,OAAOsC;YACT;YACA3B,SAAS,CAACE,MAAMU;gBACdhC,sBAAsBmC,GAAG,CAACH,aAAaV;YACzC;YACAD,QAAQ,CAAC2B,OAAOhB;gBACdhC,sBAAsBwC,MAAM,CAACR;YAC/B;QACF;QAEArB,SAASoB,KAAK,CAACC,WAAW,CAACT,GAAG,CAC5B;YAAEC,MAAM3B;YAAY4B,OAAO,CAACC;QAAS,GACrC,CAACM;YACCA,YAAYD,KAAK,CAACkB,WAAW,CAAC1B,GAAG,CAAC1B,YAAY,CAACqD;oBASxBlB;gBARrB,MAAMmB,aAAa,AAAC,CAAA;oBAClB,IAAI,CAACD,QAAOE,WAAW,EAAE;wBACvB,OAAO;oBACT;oBAEA,OAAOF,QAAOE,WAAW,CAACC,KAAK,CAAC,KAAKC,GAAG;gBAC1C,CAAA;gBAEA,MAAMC,eAAevB,gCAAAA,2BAAAA,YAAawB,WAAW,qBAAxBxB,yBAA0ByB,SAAS,CAACP;gBAEzD,IAAI5B;gBAEJ,MAAMoC,cAAc3D,yBAAyBmC,GAAG,CAACF;gBACjD,MAAMjB,WAAW,CAAC,YAAY,EAAEoC,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,GAAG,GAAG,CAAC;gBACpE,MAAMQ,aACJJ,iBAAgBG,+BAAAA,YAAaxB,GAAG,CAACqB;gBACnC,IAAII,YAAY;oBACdrC,OAAOqC,WAAW9B,UAAU,CAACd;gBAC/B,OAAO;oBACL,IAAIG;oBACJ,KAAK,MAAM0C,sBAAsB5B,YAAYwB,WAAW,CAACK,sBAAsB,CAC7EX,SACC;wBACD,MAAMY,YAAYpE,MAAMwC,GAAG,CAAC0B,mBAAmBG,UAAU;wBACzD,IAAID,WAAW;4BACb5C,aAAa4C;4BACb;wBACF;oBACF;oBAEA,IAAI,CAAC5C,YAAY;wBACf,MAAM6B,kBAAkBrD,MAAMwC,GAAG,CAACF;wBAClC,IAAI,CAACe,iBAAiB;4BACpB;wBACF;wBAEA7B,aAAa6B;oBACf;oBACAzB,OAAOJ,WAAWW,UAAU,CAACd;gBAC/B;gBACAO,KAAK0C,YAAY,CAAC,QAAQd,QAAOE,WAAW;gBAC5C9B,KAAK0C,YAAY,CAAC,SAASd,QAAOe,KAAK;gBACvCP,YAAavB,GAAG,CAACe,SAAQ5B;YAC3B;YAEA,MAAM4C,cAAcC,qBAAY,CAACC,mBAAmB,CAACpC;YACrDkC,YAAYG,YAAY,CAACC,GAAG,CAACC,WAAWC,SAAS,CAAC;gBAChDC,UAASC,OAAY;oBACnB,MAAMC,KAAKD,QAAQC,EAAE;oBACrBD,QAAQC,EAAE,GAAG,CAACC,eAAoBC;wBAChC,MAAMC,aACJF,cAAcG,gBAAgB,CAAClD,UAAU,CAAC,CAAC,aAAa,CAAC;wBAC3D8C,GAAGC,eAAe,CAACI,KAAUC;4BAC3BH,WAAWhD,IAAI;4BACf+C,SAASG,KAAKC;wBAChB;oBACF;oBACA,OAAOP;gBACT;YACF;YAEAR,YAAYgB,MAAM,CAAC3D,GAAG,CACpB1B,YACA,CAAC+E,eAAoB1B;oBACAnD;gBAAnB,MAAM+E,cAAa/E,gCAAAA,yBAChBmC,GAAG,CAACF,iCADYjC,8BAEfmC,GAAG,CAACgB;gBACR0B,cAAcG,gBAAgB,GAAGD;YACnC;YAGF9C,YAAYD,KAAK,CAACoD,aAAa,CAAC5D,GAAG,CAAC1B,YAAY,CAACqD;oBAC/CnD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BmC,GAAG,CAACF,kCAA9BjC,oCAAAA,8BAA4CmC,GAAG,CAACgB,6BAAhDnD,kCAAyD+B,IAAI;YAC/D;YACAE,YAAYD,KAAK,CAACqD,YAAY,CAAC7D,GAAG,CAAC1B,YAAY,CAACqD;oBAC9CnD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BmC,GAAG,CAACF,kCAA9BjC,oCAAAA,8BAA4CmC,GAAG,CAACgB,6BAAhDnD,kCAAyD+B,IAAI;YAC/D;YAEA,IAAI,CAAChB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAACsD,IAAI,EACtBrD,YAAYD,KAAK,CAACuD,SAAS,EAC3B;gBACEpE,YAAY,IAAMxB,MAAMwC,GAAG,CAACF;gBAC5BZ,SAAQE,IAAI;oBACVrB,sBAAsBkC,GAAG,CAACH,aAAaV;gBACzC;gBACAD;oBACEpB,sBAAsBuC,MAAM,CAACR;gBAC/B;YACF;YAGFA,YAAYD,KAAK,CAACwD,QAAQ,CAAChE,GAAG,CAAC1B,YAAY,CAAC2F;gBAC1C,MAAMtE,aACJlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;gBACtD,IAAI,CAACd,YAAY;oBACf;gBACF;gBACA,MAAMuE,eAAevE,WAAWW,UAAU,CAAC;gBAC3C4D,aAAazB,YAAY,CAAC,WAAWwB,MAAME,OAAO;gBAClDhG,MAAMyC,GAAG,CAACqD,OAAOC;YACnB;YAEAzD,YAAYD,KAAK,CAAC4D,YAAY,CAACpE,GAAG,CAAC1B,YAAY,CAAC2F;oBAC9C9F;iBAAAA,aAAAA,MAAMwC,GAAG,CAACsD,2BAAV9F,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACgD;YACf;YACAxD,YAAYD,KAAK,CAAC6D,WAAW,CAACrE,GAAG,CAAC1B,YAAY,CAAC2F;oBAC7C9F;iBAAAA,aAAAA,MAAMwC,GAAG,CAACsD,2BAAV9F,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACgD;YACf;YAEA,IAAI,CAAC1E,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAAC8D,YAAY,EAC9B7D,YAAYD,KAAK,CAAC+D,WAAW,EAC7B;gBACE5E,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,YACAkB,YAAYD,KAAK,CAACgE,QAAQ,EAC1B/D,YAAYD,KAAK,CAACiE,aAAa,EAC/B;gBACE9E,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,oBACAkB,YAAYD,KAAK,CAACkE,eAAe,EACjCjE,YAAYD,KAAK,CAACmE,oBAAoB,EACtC;gBACEhF,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAACoE,cAAc,EAChCnE,YAAYD,KAAK,CAACqE,mBAAmB,EACrC;gBACElF,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,iBACAkB,YAAYD,KAAK,CAACsE,YAAY,EAC9BrE,YAAYD,KAAK,CAACuE,iBAAiB,EACnC;gBACEpF,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,0BACAkB,YAAYD,KAAK,CAACwE,oBAAoB,EACtCvE,YAAYD,KAAK,CAACyE,yBAAyB,EAC3C;gBACEtF,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAAC0E,gBAAgB,EAClCzE,YAAYD,KAAK,CAAC2E,eAAe,EACjC;gBACExF,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAAC4E,oBAAoB,EACtC3E,YAAYD,KAAK,CAAC6E,mBAAmB,EACrC;gBACE1F,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAAC8E,UAAU,EAC5B7E,YAAYD,KAAK,CAAC+E,SAAS,EAC3B;gBACE5F,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,wBACAkB,YAAYD,KAAK,CAAC+E,SAAS,EAC3B9E,YAAYD,KAAK,CAACgF,kBAAkB,EACpC;gBACE7F,YAAY,IACVjB,sBAAsBiC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAGF,MAAMgF,OAAO,IAAIC;YACjB,MAAMC,eAAelF,YAAYmF,MAAM,CAACC,IAAI;YAC5C,MAAMC,kBAAkBrF,YAAYmF,MAAM,CAACG,OAAO;YAElDtF,YAAYmF,MAAM,CAACC,IAAI,GAAG,CAAChH;gBACzB,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAO8G,aAAaK,IAAI,CAACvF,YAAYmF,MAAM,EAAE/G;gBAC/C;gBACA,MAAMkB,OAAOrB,sBAAsBiC,GAAG,CAACF;gBACvC,IAAIV,MAAM;oBACR0F,KAAK7E,GAAG,CAAC/B,OAAOkB,KAAKO,UAAU,CAACzB,MAAMoH,OAAO,CAAC,MAAM;gBACtD;gBACA,OAAON,aAAaK,IAAI,CAACvF,YAAYmF,MAAM,EAAE/G;YAC/C;YACA4B,YAAYmF,MAAM,CAACG,OAAO,GAAG,CAAClH;gBAC5B,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAOiH,gBAAgBE,IAAI,CAACvF,YAAYmF,MAAM,EAAE/G;gBAClD;gBAEA,MAAMkB,OAAO0F,KAAK9E,GAAG,CAAC9B;gBACtB,IAAIkB,MAAM;oBACRA,KAAKQ,IAAI;oBACTkF,KAAKxE,MAAM,CAACpC;gBACd;gBACA,OAAOiH,gBAAgBE,IAAI,CAACvF,YAAYmF,MAAM,EAAE/G;YAClD;QACF;IAEJ;AACF"}