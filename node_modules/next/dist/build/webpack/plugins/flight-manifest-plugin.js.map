{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "names": ["ClientReferenceManifestPlugin", "pluginState", "getProxiedPluginState", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "getDeploymentIdQueryOrEmptyString", "chunks", "for<PERSON>ach", "chunk", "SYSTEM_ENTRYPOINTS", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "encodeURI", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "PLUGIN_NAME", "constructor", "options", "dev", "appDir", "appDirBase", "path", "dirname", "sep", "Set", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "publicPath", "Error", "prefix", "rootMainFiles", "entrypoints", "get", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getFiles", "add", "chunkGroups", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "requiredChunks", "recordModule", "mod", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "resource", "type", "_identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "ssrNamedModuleId", "relative", "resourceResolveData", "isAsyncModule", "esmResource", "addClientReference", "exportName", "async", "edgeExportName", "addSSRIdMapping", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "request", "includes", "connections", "moduleGraph", "getOutgoingConnections", "connection", "dependency", "clientEntryMod", "getResolvedModule", "modId", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "normalizePagePath", "length", "CLIENT_REFERENCE_MANIFEST", "sources", "RawSource"], "mappings": "AAAA;;;;;CAKC;;;;+BA6JYA;;;eAAAA;;;8DA3JI;yBACgB;2BAI1B;8BAE+B;4BAEP;mCACG;8BAEgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBlD,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxCC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IACtBC,sBAAsB,EAAE;AAC1B;AA4CA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBC,IAAAA,+CAAiC;IAEhE,MAAMC,SAAwB,EAAE;IAChCJ,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAIC,6BAAkB,CAACC,GAAG,CAACF,MAAMG,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIH,MAAMI,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKL,MAAMI,EAAE;YAC7BJ,MAAMM,KAAK,CAACP,OAAO,CAAC,CAACQ;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIb,cAAcO,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOT,OAAOW,IAAI,CAACJ,SAASK,UAAUH,OAAOX;YAC/C;QACF;IACF;IACA,OAAOE;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,SAASa,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,MAC/BC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B;IAErC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;AACrE;AAEA,MAAMC,cAAc;AAEb,MAAMxC;IAMXyC,YAAYC,OAAgB,CAAE;aAL9BC,MAAsB;QAMpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGC,aAAI,CAACC,OAAO,CAAC,IAAI,CAACH,MAAM,IAAIE,aAAI,CAACE,GAAG;QACtD,IAAI,CAAC3C,oBAAoB,GAAG,IAAI4C,IAAIhD,YAAYI,oBAAoB;IACtE;IAEA6C,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5Bd,aACA,CAACa,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;YAElDV,YAAYD,KAAK,CAACY,aAAa,CAACV,GAAG,CACjC;gBACEtC,MAAMwB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CyB,OAAOP,gBAAO,CAACQ,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,WAAW,CAACD,QAAQf,aAAaF,SAASmB,OAAO;QAEtE;IAEJ;IAEAD,YACED,MAAqC,EACrCf,WAAgC,EAChCiB,OAAe,EACf;YAuBAjB;QAtBA,MAAMkB,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJrB,YAAYsB,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,cACF;QAEN,IAAI,OAAOrB,YAAYsB,aAAa,CAACG,UAAU,KAAK,UAAU;YAC5D,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMC,SAAS3B,YAAYsB,aAAa,CAACG,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAIhC;SACvCI,+BAAAA,YAAY6B,WAAW,CACpBC,GAAG,CAACC,+CAAoC,sBAD3C/B,6BAEIgC,QAAQ,GACTzE,OAAO,CAAC,CAACQ;YACR,IAAI,oCAAoCU,IAAI,CAACV,OAAO;gBAClD6D,cAAcK,GAAG,CAAClE,KAAKS,OAAO,CAAC,OAAO;YACxC;QACF;QAEFwB,YAAYkC,WAAW,CAAC3E,OAAO,CAAC,CAACL;YAC/B,mEAAmE;YACnE,IAAIkB,YAAY;YAChB,MAAMO,WAAoC;gBACxCwD,eAAe;oBACbR;oBACAS,aAAaZ;gBACf;gBACAxC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,IAAIhC,WAAWS,IAAI,IAAI,YAAYc,IAAI,CAACvB,WAAWS,IAAI,GAAG;gBACxD,sCAAsC;gBACtC,MAAM0E,iBAAiB,AAAC,CAAA,IAAI,CAAC7C,UAAU,GAAGtC,WAAWS,IAAI,AAAD,EAAGa,OAAO,CAChE,UACAiB,aAAI,CAACE,GAAG;gBAEVhB,SAASO,aAAa,CAACmD,eAAe,GAAGnF,WACtC8E,QAAQ,GACRM,MAAM,CACL,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAEvE,QAAQ,CAAC;gBAG5DI,YAAYlB,WAAWS,IAAI;YAC7B;YAEA,MAAM8E,iBAAiBxF,yBAAyBC,YAAY0E;YAC5D,MAAMc,eAAe,CAAC9E,IAAc+E;oBAyBhCA;gBAxBF,0CAA0C;gBAC1C,IAAIA,IAAIC,KAAK,KAAKC,0BAAc,CAACC,eAAe,EAAE;oBAChD;gBACF;gBAEA,MAAMC,WACJJ,IAAIK,IAAI,KAAK,qBAETL,IAAIM,WAAW,CAAC3E,KAAK,CAACqE,IAAIM,WAAW,CAAC1E,WAAW,CAAC,OAAO,KACzDoE,IAAII,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBvE,SAASI,aAAa;gBAC/C,MAAMoE,kBAAkBxE,SAASK,gBAAgB;gBACjD,MAAMoE,sBAAsBzE,SAASM,oBAAoB;gBAEzD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAIoE,mBAAmBC,IAAAA,cAAQ,EAC7BrC,SACA0B,EAAAA,2BAAAA,IAAIY,mBAAmB,qBAAvBZ,yBAAyBlD,IAAI,KAAIsD;gBAGnC,IAAI,CAACM,iBAAiBb,UAAU,CAAC,MAC/Ba,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB7E,OAAO,CAAC,OAAO,KAAK,CAAC;gBAEhE,MAAMgF,gBAAgB,IAAI,CAACxG,oBAAoB,CAACU,GAAG,CAACiF,IAAII,QAAQ;gBAEhE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMU,cAAc,0BAA0BhF,IAAI,CAACsE,YAC/CA,SAASvE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOiB,aAAI,CAACE,GAAG,KAE3C;gBAEJ,SAAS+D;oBACP,MAAMC,aAAaZ;oBACnBpE,SAASI,aAAa,CAAC4E,WAAW,GAAG;wBACnC/F;wBACAD,MAAM;wBACNL,QAAQmF;wBACRmB,OAAOJ;oBACT;oBACA,IAAIC,aAAa;wBACf,MAAMI,iBAAiBJ;wBACvB9E,SAASI,aAAa,CAAC8E,eAAe,GACpClF,SAASI,aAAa,CAAC4E,WAAW;oBACtC;gBACF;gBAEA,SAASG;oBACP,MAAMH,aAAaZ;oBACnB,IACE,OAAOnG,YAAYE,eAAe,CAACuG,iBAAiB,KAAK,aACzD;wBACAF,eAAe,CAACvF,GAAG,GAAGuF,eAAe,CAACvF,GAAG,IAAI,CAAC;wBAC9CuF,eAAe,CAACvF,GAAG,CAAC,IAAI,GAAG;4BACzB,GAAGe,SAASI,aAAa,CAAC4E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCrG,QAAQ,EAAE;4BACVM,IAAIhB,YAAYE,eAAe,CAACuG,iBAAiB;wBACnD;oBACF;oBAEA,IACE,OAAOzG,YAAYG,mBAAmB,CAACsG,iBAAiB,KACxD,aACA;wBACAD,mBAAmB,CAACxF,GAAG,GAAGwF,mBAAmB,CAACxF,GAAG,IAAI,CAAC;wBACtDwF,mBAAmB,CAACxF,GAAG,CAAC,IAAI,GAAG;4BAC7B,GAAGe,SAASI,aAAa,CAAC4E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCrG,QAAQ,EAAE;4BACVM,IAAIhB,YAAYG,mBAAmB,CAACsG,iBAAiB;wBACvD;oBACF;gBACF;gBAEAK;gBACAI;gBAEAnF,SAASI,aAAa,GAAGmE;gBACzBvE,SAASK,gBAAgB,GAAGmE;gBAC5BxE,SAASM,oBAAoB,GAAGmE;YAClC;YAEA,0EAA0E;YAC1E,oEAAoE;YACpE,oEAAoE;YACpE,qDAAqD;YACrD,6CAA6C;YAC7ClG,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;gBACzB,MAAMuG,YACJ/D,YAAYgE,UAAU,CAACC,4BAA4B,CAACzG;gBACtD,KAAK,MAAMmF,OAAOoB,UAAW;oBAC3B,IAAIpB,IAAIC,KAAK,KAAKC,0BAAc,CAACC,eAAe,EAAE;oBAElD,MAAMoB,UAAU,AAACvB,IAA6BuB,OAAO;oBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;wBACA;oBACF;oBAEA,MAAMC,cACJpE,YAAYqE,WAAW,CAACC,sBAAsB,CAAC3B;oBAEjD,KAAK,MAAM4B,cAAcH,YAAa;wBACpC,MAAMI,aAAaD,WAAWC,UAAU;wBACxC,IAAI,CAACA,YAAY;wBAEjB,MAAMC,iBAAiBzE,YAAYqE,WAAW,CAACK,iBAAiB,CAC9DF;wBAEF,MAAMG,QAAQ3E,YAAYgE,UAAU,CAACY,WAAW,CAACH;wBAKjD,IAAIE,UAAU,MAAM;4BAClBjC,aAAaiC,OAAOF;wBACtB,OAAO;gCAGHF;4BAFF,oEAAoE;4BACpE,IACEA,EAAAA,qBAAAA,WAAWM,MAAM,qBAAjBN,mBAAmBnF,WAAW,CAACzB,IAAI,MAAK,sBACxC;gCACA,MAAMmH,kBAAkBP,WAAWM,MAAM;gCACzC,MAAME,oBACJ/E,YAAYgE,UAAU,CAACY,WAAW,CAACE;gCACrCpC,aAAaqC,mBAAmBN;4BAClC;wBACF;oBACF;gBACF;YACF;YAEA,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBhG,IAAI,CAACL,YAAY;gBACvCgD,mBAAmBnD,IAAI,CAACG,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,4CAA4C;YAC5C,qBAAqB;YACrB,uBAAuB;YACvB,IAAI,+BAA+BC,IAAI,CAACL,YAAY;gBAClDgD,mBAAmBnD,IAAI,CAAC,IAAI,CAACqB,GAAG,GAAG,kBAAkB;YACvD;YAEA,MAAMjB,YAAYF,qBAAqBC;YACvC,IAAI,CAAC8C,kBAAkBxD,GAAG,CAACW,YAAY;gBACrC6C,kBAAkBd,GAAG,CAAC/B,WAAW,EAAE;YACrC;YACA6C,kBAAkBY,GAAG,CAACzD,WAAYJ,IAAI,CAACU;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMqG,YAAY5D,mBAAoB;YACzC,MAAM6D,iBAA0C;gBAC9C9C,eAAe;oBACbR;oBACAS,aAAaZ;gBACf;gBACAxC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,MAAMgG,WAAW;mBAAI/G,qBAAqB6G,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMvG,YAAYuC,kBAAkBY,GAAG,CAACsD,UAAU,EAAE,CAAE;oBACzD1G,cAAcuG,gBAAgBtG;gBAChC;gBACAyG,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAASxG,OAAO,CAAC,QAAQ;YAC1C,MAAMkH,iBAAiBC,IAAAA,oCAAiB,EAACF,SAASnH,KAAK,CAAC,MAAMsH,MAAM;YACpE7E,MAAM,CACJ,eAAe2E,iBAAiB,MAAMG,oCAAyB,GAAG,MACnE,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvB,CAAC,oFAAoF,EAAER,KAAKC,SAAS,CACnGC,SAASnH,KAAK,CAAC,MAAMsH,MAAM,GAC3B,EAAE,EAAEN,KAAK,CAAC;YAGd,IAAIG,aAAa,iBAAiB;gBAChC,kEAAkE;gBAClE1E,MAAM,CAAC,2BAA2B8E,oCAAyB,GAAG,MAAM,GAClE,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,oFAAoF,EAAER,KAAKC,SAAS,CACnG,eACA,EAAE,EAAEF,KAAK,CAAC;YAElB;QACF;QAEA1I,YAAYI,oBAAoB,GAAG,EAAE;IACvC;AACF"}