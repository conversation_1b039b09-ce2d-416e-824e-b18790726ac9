{"version": 3, "sources": ["../../src/telemetry/ci-info.ts"], "names": ["isCI", "name", "hasNextSupport", "_isCI", "_name", "ciEnvironment", "isZeitNow", "process", "env", "NOW_BUILDER", "envStack", "STACK", "isHeroku", "toLowerCase", "includes", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;IAUaA,IAAI;eAAJA;;IACAC,IAAI;eAAJA;;IAKAC,cAAc;eAAdA;;;+DAhBa;;;;;;AAE1B,MAAM,EAAEF,MAAMG,KAAK,EAAEF,MAAMG,KAAK,EAAE,GAAGC,eAAa;AAElD,MAAMC,YAAY,CAAC,CAACC,QAAQC,GAAG,CAACC,WAAW;AAE3C,MAAMC,WAAWH,QAAQC,GAAG,CAACG,KAAK;AAClC,MAAMC,WACJ,OAAOF,aAAa,YAAYA,SAASG,WAAW,GAAGC,QAAQ,CAAC;AAE3D,MAAMd,OAAOM,aAAaM,YAAYT;AACtC,MAAMF,OAAOK,YAAY,aAAaM,WAAW,WAAWR;AAK5D,MAAMF,iBAAiBa,QAAQT"}