{"name": "react-dom-experimental-builtin", "main": "index.js", "exports": {".": {"react-server": "./react-dom.shared-subset.js", "default": "./index.js"}, "./client": "./client.js", "./server": {"workerd": "./server.edge.js", "bun": "./server.bun.js", "deno": "./server.browser.js", "worker": "./server.browser.js", "browser": "./server.browser.js", "node": "./server.node.js", "edge-light": "./server.edge.js", "default": "./server.node.js"}, "./server.browser": "./server.browser.js", "./server.bun": "./server.bun.js", "./server.edge": "./server.edge.js", "./server.node": "./server.node.js", "./static": {"workerd": "./static.edge.js", "deno": "./static.browser.js", "worker": "./static.browser.js", "browser": "./static.browser.js", "node": "./static.node.js", "edge-light": "./static.edge.js", "default": "./static.node.js"}, "./static.browser": "./static.browser.js", "./static.edge": "./static.edge.js", "./static.node": "./static.node.js", "./server-rendering-stub": "./server-rendering-stub.js", "./profiling": "./profiling.js", "./test-utils": "./test-utils.js", "./unstable_testing": "./unstable_testing.js", "./unstable_server-external-runtime": "./unstable_server-external-runtime.js", "./package.json": "./package.json"}, "dependencies": {"loose-envify": "^1.1.0", "scheduler": "0.0.0-experimental-2c338b16f-20231116"}, "peerDependencies": {"react": "0.0.0-experimental-2c338b16f-20231116"}, "browser": {"./server.js": "./server.browser.js", "./static.js": "./static.browser.js"}}