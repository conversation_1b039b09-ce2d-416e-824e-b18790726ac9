{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["normalizeVercelUrl", "interpolateDynamicPath", "normalizeDynamicRouteParams", "getUtils", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "parseUrl", "url", "search", "key", "Object", "keys", "query", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "groups", "includes", "formatUrl", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "ignoreOptional", "defaultRouteMatches", "hasValidParams", "reduce", "prev", "normalizeRscURL", "val", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "undefined", "split", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "isCatchAll", "_val", "item", "toLowerCase", "locale", "splice", "every", "name", "keyName", "paramName", "pos", "parseInt", "headers"], "mappings": ";;;;;;;;;;;;;;;;;IAoBgBA,kBAAkB;eAAlBA;;IA0BAC,sBAAsB;eAAtBA;;IAuCAC,2BAA2B;eAA3BA;;IAkFAC,QAAQ;eAARA;;;qBAhKuC;qCACnB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BACQ;AAEjC,SAASH,mBACdI,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAaC,IAAAA,UAAQ,EAACN,IAAIO,GAAG,EAAG;QACtC,OAAO,AAACF,WAAmBG,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACN,WAAWO,KAAK,EAAG;YAC/C,IACE,AAACH,QAAQI,kCAAuB,IAC9BJ,IAAIK,UAAU,CAACD,kCAAuB,KACxC,AAACX,CAAAA,aAAaQ,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,CAAA,EAAGC,QAAQ,CAACP,MAC9D;gBACA,OAAOJ,WAAWO,KAAK,CAACH,IAAI;YAC9B;QACF;QACAT,IAAIO,GAAG,GAAGU,IAAAA,WAAS,EAACZ;IACtB;AACF;AAEO,SAASR,uBACdqB,QAAgB,EAChBC,MAAsB,EACtBf,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOc;IAE/B,KAAK,MAAME,SAASV,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGlB,kBAAkBW,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEO,SAASpB,4BACdqB,MAAsB,EACtBiB,cAAwB,EACxBhC,iBAAqE,EACrEiC,mBAAgD;IAEhD,IAAIC,iBAAiB;IACrB,IAAI,CAAClC,mBAAmB,OAAO;QAAEe;QAAQmB,gBAAgB;IAAM;IAE/DnB,SAAST,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,EAAEwB,MAAM,CAAC,CAACC,MAAM/B;QAC3D,IAAIkB,QAAuCR,MAAM,CAACV,IAAI;QAEtD,IAAI,OAAOkB,UAAU,UAAU;YAC7BA,QAAQc,IAAAA,yBAAe,EAACd;QAC1B;QACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACY;gBACjB,IAAI,OAAOA,QAAQ,UAAU;oBAC3BA,MAAMD,IAAAA,yBAAe,EAACC;gBACxB;gBACA,OAAOA;YACT;QACF;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeN,mBAAoB,CAAC5B,IAAI;QAC9C,MAAMmC,aAAaxC,kBAAmBW,MAAM,CAACN,IAAI,CAACY,QAAQ;QAE1D,MAAMwB,iBAAiBjB,MAAMC,OAAO,CAACc,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOnB,MAAMC,OAAO,CAACF,SACjBA,MAAMmB,IAAI,CAAC,CAACJ,MAAQA,IAAI1B,QAAQ,CAAC+B,eACjCpB,yBAAAA,MAAOX,QAAQ,CAAC+B;QACtB,KACApB,yBAAAA,MAAOX,QAAQ,CAAC2B;QAEpB,IACEE,kBACC,OAAOlB,UAAU,eAAe,CAAEiB,CAAAA,cAAcR,cAAa,GAC9D;YACAE,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEM,cACC,CAAA,CAACjB,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAElB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAkB,QAAQqB;YACR,OAAO7B,MAAM,CAACV,IAAI;QACpB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEkB,SACA,OAAOA,UAAU,YACjBvB,kBAAmBW,MAAM,CAACN,IAAI,CAACa,MAAM,EACrC;YACAK,QAAQA,MAAMsB,KAAK,CAAC;QACtB;QAEA,IAAItB,OAAO;YACTa,IAAI,CAAC/B,IAAI,GAAGkB;QACd;QACA,OAAOa;IACT,GAAG,CAAC;IAEJ,OAAO;QACLrB;QACAmB;IACF;AACF;AAEO,SAASvC,SAAS,EACvBmD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRlD,aAAa,EACbmD,aAAa,EACbC,aAAa,EAad;IACC,IAAInD;IACJ,IAAIoD;IACJ,IAAInB;IAEJ,IAAIlC,eAAe;QACjBC,oBAAoBqD,IAAAA,8BAAkB,EAACP,MAAM;QAC7CM,sBAAsBE,IAAAA,6BAAe,EAACtD;QACtCiC,sBAAsBmB,oBAAoBN;IAC5C;IAEA,SAASS,eAAe3D,GAAoB,EAAE4D,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAU1C,QAAQ;QAEnC,MAAM6C,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAACf,UAC1CM,uCAAAA,oBAAsBQ;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIhB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEiB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAAClB;YACf;YAEF,IAAIpC,SAASiD,QAAQR,UAAU1C,QAAQ;YAEvC,IAAI,AAACiD,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAMxD,QAAQ;gBAC9C,MAAMyD,YAAYC,IAAAA,4BAAQ,EACxB7E,KACA4D,UAAUhD,KAAK,EACfuD,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACblE,OAAOoE,MAAM,CAAC3D,QAAQyD;gBACxB,OAAO;oBACLzD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE4D,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAahB,QAAQgB,WAAW;oBAChChE,QAAQA;oBACRP,OAAOgD,UAAUhD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAImE,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA1E,OAAOoE,MAAM,CAACjB,eAAemB,WAAW7D;gBACxCT,OAAOoE,MAAM,CAAClB,UAAUhD,KAAK,EAAEmE,kBAAkBnE,KAAK;gBACtD,OAAO,AAACmE,kBAA0BnE,KAAK;gBAEvCF,OAAOoE,MAAM,CAAClB,WAAWmB;gBAEzBjB,aAAaF,UAAU1C,QAAQ;gBAE/B,IAAIkC,UAAU;oBACZU,aACEA,WAAYuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAElC,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAMoC,uBAAuBC,IAAAA,wCAAmB,EAC9C1B,YACAX,KAAKsC,OAAO;oBAEd3B,aAAayB,qBAAqBrE,QAAQ;oBAC1C0C,UAAUhD,KAAK,CAAC8E,kBAAkB,GAChCH,qBAAqBI,cAAc,IAAIxE,OAAOuE,kBAAkB;gBACpE;gBAEA,IAAI5B,eAAeZ,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAI/C,iBAAiBqD,qBAAqB;oBACxC,MAAMoC,gBAAgBpC,oBAAoBM;oBAC1C,IAAI8B,eAAe;wBACjBhC,UAAUhD,KAAK,GAAG;4BAChB,GAAGgD,UAAUhD,KAAK;4BAClB,GAAGgF,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMzB,WAAWd,SAASwC,WAAW,IAAI,EAAE,CAAE;YAChD3B,aAAaC;QACf;QAEA,IAAIL,eAAeZ,MAAM;YACvB,IAAI4C,WAAW;YAEf,KAAK,MAAM3B,WAAWd,SAAS0C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAW5B,aAAaC;gBACxB,IAAI2B,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC/B,eAAe;gBAC/B,KAAK,MAAMI,WAAWd,SAAS2C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAW5B,aAAaC;oBACxB,IAAI2B,UAAU;gBAChB;YACF;QACF;QACA,OAAOjC;IACT;IAEA,SAASoC,0BACPjG,GAAoB,EACpBkG,UAAgB,EAChBP,cAAuB;QAEvB,OAAOjC,IAAAA,6BAAe,EACpB,AAAC;YACC,MAAM,EAAE3C,MAAM,EAAEoF,SAAS,EAAE,GAAG/F;YAE9B,OAAO;gBACLgG,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAM7F,OAAO8F,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJvD,QAAQwC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAMlF,OAAOC,OAAOC,IAAI,CAAC4F,KAAM;4BAClC,MAAM5E,QAAQ4E,GAAG,CAAC9F,IAAI;4BAEtB,IACEA,QAAQI,kCAAuB,IAC/BJ,IAAIK,UAAU,CAACD,kCAAuB,GACtC;gCACA,MAAM8F,gBAAgBlG,IAAImG,SAAS,CACjC/F,kCAAuB,CAACsB,MAAM;gCAEhCoE,GAAG,CAACI,cAAc,GAAGhF;gCACrB,OAAO4E,GAAG,CAAC9F,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAMoG,gBAAgBnG,OAAOC,IAAI,CAACwF,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAACpE;4BACxB,IAAIS,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAM4D,aAAanF,MAAMC,OAAO,CAACa;gCACjC,MAAMsE,OAAOD,aAAarE,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOsE,SAAS,YAChB7D,KAAKsC,OAAO,CAAC3C,IAAI,CAAC,CAACmE;oCACjB,IAAIA,KAAKC,WAAW,OAAOF,KAAKE,WAAW,IAAI;wCAC7CvB,iBAAiBsB;wCACjBf,WAAWiB,MAAM,GAAGxB;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIoB,YAAY;wCACZrE,IAAiB0E,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAOL,aAAarE,IAAIP,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAI0E,cAAcQ,KAAK,CAAC,CAACC,OAASf,GAAG,CAACe,KAAK,GAAG;4BAC5C,OAAOT,cAActE,MAAM,CAAC,CAACC,MAAM+E;gCACjC,MAAMC,YAAYrB,6BAAAA,SAAW,CAACoB,QAAQ;gCAEtC,IAAIC,aAAa,CAACV,iBAAiBP,GAAG,CAACgB,QAAQ,GAAG;oCAChD/E,IAAI,CAACzB,MAAM,CAACyG,UAAU,CAACC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,QAAQ;gCAC5C;gCACA,OAAO/E;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAO9B,OAAOC,IAAI,CAAC4F,KAAKhE,MAAM,CAAC,CAACC,MAAM/B;4BACpC,IAAI,CAACqG,iBAAiBP,GAAG,CAAC9F,IAAI,GAAG;gCAC/B,IAAIkG,gBAAgBlG;gCAEpB,IAAIiG,kBAAkB;oCACpBC,gBAAgBe,SAASjH,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOoE,MAAM,CAACtC,MAAM;oCACzB,CAACmE,cAAc,EAAEJ,GAAG,CAAC9F,IAAI;gCAC3B;4BACF;4BACA,OAAO+B;wBACT,GAAG,CAAC;oBACN;gBACF;gBACAzB;YACF;QACF,KACAf,IAAI2H,OAAO,CAAC,sBAAsB;IACtC;IAEA,OAAO;QACLhE;QACAvD;QACAoD;QACAnB;QACA4D;QACAnG,6BAA6B,CAC3BqB,QACAiB,iBAEAtC,4BACEqB,QACAiB,gBACAhC,mBACAiC;QAEJzC,oBAAoB,CAClBI,KACAC,YACAC,YAEAN,mBACEI,KACAC,YACAC,WACAC,eACAC;QAEJP,wBAAwB,CACtBqB,UACAC,SACGtB,uBAAuBqB,UAAUC,QAAQf;IAChD;AACF"}