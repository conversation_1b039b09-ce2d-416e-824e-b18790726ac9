{"version": 3, "sources": ["../../src/server/config-shared.ts"], "names": ["defaultConfig", "normalizeConfig", "env", "webpack", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "poweredByHeader", "compress", "analyticsId", "process", "VERCEL_ANALYTICS_ID", "images", "imageConfigDefault", "devIndicators", "buildActivity", "buildActivityPosition", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "optimizeFonts", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactProductionProfiling", "reactStrictMode", "httpAgentOptions", "keepAlive", "outputFileTracing", "staticPageGenerationTimeout", "swcMinify", "output", "NEXT_PRIVATE_STANDALONE", "undefined", "modularizeImports", "experimental", "windowHistorySupport", "serverMinification", "serverSourceMaps", "caseSensitiveRoutes", "useDeploymentId", "deploymentId", "useDeploymentIdServerActions", "appDocumentPreloading", "clientRouterFilter", "clientRouterFilterRedirects", "fetchCacheKeyPrefix", "middlewarePrefetch", "optimisticClientCache", "manualClientBasePath", "cpus", "Math", "max", "Number", "CIRCLE_NODE_TOTAL", "os", "length", "memoryBasedWorkersCount", "isrFlushToDisk", "workerThreads", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "craCompat", "esmExternals", "isrMemoryCacheSize", "incremental<PERSON>ache<PERSON>andlerPath", "fullySpecified", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "turbo", "turbotrace", "typedRoutes", "instrumentationHook", "bundlePagesExternals", "ppr", "__NEXT_TEST_MODE", "__NEXT_EXPERIMENTAL_PPR", "webpackBuildWorker", "phase", "config"], "mappings": ";;;;;;;;;;;;;;;IAwrBaA,aAAa;eAAbA;;IAoHSC,eAAe;eAAfA;;;2DA5yBP;6BAGoB;;;;;;AAqrB5B,MAAMD,gBAA4B;IACvCE,KAAK,CAAC;IACNC,SAAS;IACTC,QAAQ;QACNC,oBAAoB;IACtB;IACAC,YAAY;QACVC,mBAAmB;QACnBC,cAAc;IAChB;IACAC,SAAS;IACTC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,2BAA2B;IAC3BC,iBAAiB,IAAM;IACvBC,eAAe;IACfC,gBAAgB;QAAC;QAAO;QAAM;QAAO;KAAK;IAC1CC,iBAAiB;IACjBC,UAAU;IACVC,aAAaC,QAAQlB,GAAG,CAACmB,mBAAmB,IAAI;IAChDC,QAAQC,+BAAkB;IAC1BC,eAAe;QACbC,eAAe;QACfC,uBAAuB;IACzB;IACAC,iBAAiB;QACfC,gBAAgB,KAAK;QACrBC,mBAAmB;IACrB;IACAC,KAAK;QACHC,eAAe;IACjB;IACAC,UAAU;IACVC,aAAa,CAAC;IACdC,eAAe;IACfC,MAAM;IACNC,6BAA6B;IAC7BC,eAAe;IACfC,6BAA6B;IAC7BC,qBAAqB,CAAC;IACtBC,qBAAqB,CAAC;IACtBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,kBAAkB;QAChBC,WAAW;IACb;IACAC,mBAAmB;IACnBC,6BAA6B;IAC7BC,WAAW;IACXC,QAAQ,CAAC,CAAC5B,QAAQlB,GAAG,CAAC+C,uBAAuB,GAAG,eAAeC;IAC/DC,mBAAmBD;IACnBE,cAAc;QACZC,sBAAsB;QACtBC,oBAAoB;QACpBC,kBAAkB;QAClBC,qBAAqB;QACrBC,iBAAiB;QACjBC,cAAcR;QACdS,8BAA8B;QAC9BC,uBAAuBV;QACvBW,oBAAoB;QACpBC,6BAA6B;QAC7BC,qBAAqB;QACrBC,oBAAoB;QACpBC,uBAAuB;QACvBC,sBAAsB;QACtBC,MAAMC,KAAKC,GAAG,CACZ,GACA,AAACC,CAAAA,OAAOlD,QAAQlB,GAAG,CAACqE,iBAAiB,KACnC,AAACC,CAAAA,WAAE,CAACL,IAAI,MAAM;YAAEM,QAAQ;QAAE,CAAA,EAAGA,MAAM,AAAD,IAAK;QAE3CC,yBAAyB;QACzBC,gBAAgB;QAChBC,eAAe;QACfC,cAAc3B;QACd4B,aAAa;QACbC,mBAAmB;QACnBC,mBAAmB;QACnBC,aAAa;QACbC,yBAAyB;QACzBC,UAAU;QACVC,WAAW;QACXC,cAAc;QACd,wBAAwB;QACxBC,oBAAoB,KAAK,OAAO;QAChCC,6BAA6BrC;QAC7BsC,gBAAgB;QAChBC,uBAAuBrE,QAAQlB,GAAG,CAACwF,8BAA8B,IAAI;QACrEC,mBAAmB;QACnBC,oBAAoB;QACpBC,YAAY3C;QACZ4C,oBAAoB,MAAM;QAC1BC,yBAAyB7C;QACzBpB,KAAKoB;QACL8C,YAAY9C;QACZ+C,qBAAqB;QACrBC,mCAAmC;QACnCC,OAAOjD;QACPkD,YAAYlD;QACZmD,aAAa;QACbC,qBAAqB;QACrBC,sBAAsB;QACtBC,KACE,2CAA2C;QAC3C,2EAA2E;QAC3E,4EAA4E;QAC5E,4CAA4C;QAC5CpF,QAAQlB,GAAG,CAACuG,gBAAgB,IAC5BrF,QAAQlB,GAAG,CAACwG,uBAAuB,KAAK,SACpC,OACA;QACNC,oBAAoB;IACtB;AACF;AAEO,eAAe1G,gBAAgB2G,KAAa,EAAEC,MAAW;IAC9D,IAAI,OAAOA,WAAW,YAAY;QAChCA,SAASA,OAAOD,OAAO;YAAE5G;QAAc;IACzC;IACA,gFAAgF;IAChF,OAAO,MAAM6G;AACf"}