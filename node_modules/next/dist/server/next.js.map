{"version": 3, "sources": ["../../src/server/next.ts"], "names": ["NextServer", "ServerImpl", "getServerImpl", "undefined", "Promise", "resolve", "require", "default", "SYMBOL_LOAD_CONFIG", "Symbol", "constructor", "options", "hostname", "port", "getRequestHandler", "req", "res", "parsedUrl", "getTracer", "trace", "NextServerSpan", "requestHandler", "getServerRequestHandler", "getUpgradeHandler", "socket", "head", "server", "getServer", "handleUpgrade", "apply", "setAssetPrefix", "assetPrefix", "preparedAssetPrefix", "logError", "args", "render", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "prepare", "serverFields", "standaloneMode", "Object", "assign", "dev", "close", "createServer", "ServerImplementation", "dir", "config", "preloadedConfig", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "customConfig", "conf", "silent", "process", "env", "NODE_ENV", "serializedConfig", "path", "join", "SERVER_FILES_MANIFEST", "experimental", "isExperimentalCompile", "_", "serverPromise", "then", "__NEXT_PRIVATE_STANDALONE_CONFIG", "JSON", "stringify", "output", "log", "warn", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reqHandlerPromise", "wrap", "bind", "NextCustomServer", "getRequestHandlers", "isNodeDebugging", "checkNodeDebugType", "initResult", "isDev", "minimalMode", "upgradeHandler", "setupWebSocketHandler", "customServer", "_req", "didWebSocketSetup", "on", "httpServer", "url", "formatUrl", "pathname", "query", "startsWith", "console", "error", "typescript", "createTSPlugin", "includes", "NON_STANDARD_NODE_ENV", "module", "exports"], "mappings": ";;;;;;;;;;;;;;;IA0DaA,UAAU;eAAVA;;IA6Ub,2BAA2B;IAE3B,oCAAoC;IACpC,OAA2B;eAA3B;;;QA/XO;QACA;6DAGc;+DACE;8DACO;2BACQ;4BAI/B;wBAEmB;4BACK;2BACL;uBACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,IAAIC;AAEJ,MAAMC,gBAAgB;IACpB,IAAID,eAAeE,WAAW;QAC5BF,aAAa,AAAC,CAAA,MAAMG,QAAQC,OAAO,CAACC,QAAQ,iBAAgB,EAAGC,OAAO;IACxE;IACA,OAAON;AACT;AAoBA,MAAMO,qBAAqBC,OAAO;AAE3B,MAAMT;IAWXU,YAAYC,OAA0B,CAAE;QACtC,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA,IAAIC,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEAC,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,0BAAc,CAACN,iBAAiB,EAAE;gBACzD,MAAMO,iBAAiB,MAAM,IAAI,CAACC,uBAAuB;gBACzD,OAAOD,eAAeN,KAAKC,KAAKC;YAClC;QACF;IACF;IAEAM,oBAAoB;QAClB,OAAO,OAAOR,KAAsBS,QAAaC;YAC/C,MAAMC,SAAS,MAAM,IAAI,CAACC,SAAS;YACnC,mDAAmD;YACnD,uBAAuB;YACvB,OAAOD,OAAOE,aAAa,CAACC,KAAK,CAACH,QAAQ;gBAACX;gBAAKS;gBAAQC;aAAK;QAC/D;IACF;IAEAK,eAAeC,WAAmB,EAAE;QAClC,IAAI,IAAI,CAACL,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACI,cAAc,CAACC;QAC7B,OAAO;YACL,IAAI,CAACC,mBAAmB,GAAGD;QAC7B;IACF;IAEAE,SAAS,GAAGC,IAAoC,EAAE;QAChD,IAAI,IAAI,CAACR,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACO,QAAQ,IAAIC;QAC1B;IACF;IAEA,MAAMC,OAAO,GAAGD,IAAkC,EAAE;QAClD,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOS,MAAM,IAAID;IAC1B;IAEA,MAAME,aAAa,GAAGF,IAAwC,EAAE;QAC9D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOU,YAAY,IAAIF;IAChC;IAEA,MAAMG,YAAY,GAAGH,IAAuC,EAAE;QAC5D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOW,WAAW,IAAIH;IAC/B;IAEA,MAAMI,kBAAkB,GAAGJ,IAA6C,EAAE;QACxE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOY,iBAAiB,IAAIJ;IACrC;IAEA,MAAMK,UAAU,GAAGL,IAAqC,EAAE;QACxD,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOa,SAAS,IAAIL;IAC7B;IAEA,MAAMM,QAAQC,YAAkB,EAAE;QAChC,IAAI,IAAI,CAACC,cAAc,EAAE;QAEzB,MAAMhB,SAAS,MAAM,IAAI,CAACC,SAAS;QAEnC,IAAIc,cAAc;YAChBE,OAAOC,MAAM,CAAClB,QAAQe;QACxB;QACA,iDAAiD;QACjD,oDAAoD;QACpD,IAAI,IAAI,CAAC9B,OAAO,CAACkC,GAAG,EAAE;YACpB,MAAMnB,OAAOc,OAAO;QACtB;IACF;IAEA,MAAMM,QAAQ;QACZ,MAAMpB,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAO,AAACD,OAAeoB,KAAK;IAC9B;IAEA,MAAcC,aACZpC,OAAyC,EACxB;QACjB,IAAIqC;QACJ,IAAIrC,QAAQkC,GAAG,EAAE;YACfG,uBAAuB1C,QAAQ,yBAAyBC,OAAO;QACjE,OAAO;YACLyC,uBAAuB,MAAM9C;QAC/B;QACA,MAAMwB,SAAS,IAAIsB,qBAAqBrC;QAExC,OAAOe;IACT;IAEA,MAAc,CAAClB,mBAAmB,GAAG;QACnC,MAAMyC,MAAM5C,IAAAA,aAAO,EAAC,IAAI,CAACM,OAAO,CAACsC,GAAG,IAAI;QAExC,MAAMC,SACJ,IAAI,CAACvC,OAAO,CAACwC,eAAe,IAC3B,MAAMC,IAAAA,eAAU,EACf,IAAI,CAACzC,OAAO,CAACkC,GAAG,GAAGQ,oCAAwB,GAAGC,mCAAuB,EACrEL,KACA;YACEM,cAAc,IAAI,CAAC5C,OAAO,CAAC6C,IAAI;YAC/BC,QAAQ;QACV;QAGJ,+CAA+C;QAC/C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI;gBACF,MAAMC,mBAAmBvD,QAAQwD,aAAI,CAACC,IAAI,CACxCd,KACA,SACAe,iCAAqB,GACpBd,MAAM;gBAET,kCAAkC;gBAClCA,OAAOe,YAAY,CAACC,qBAAqB,GACvCL,iBAAiBI,YAAY,CAACC,qBAAqB;YACvD,EAAE,OAAOC,GAAG;YACV,kDAAkD;YAClD,oDAAoD;YACpD,sBAAsB;YACxB;QACF;QAEA,OAAOjB;IACT;IAEA,MAAcvB,YAAY;QACxB,IAAI,CAAC,IAAI,CAACyC,aAAa,EAAE;YACvB,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC5D,mBAAmB,GAAG6D,IAAI,CAAC,OAAOb;gBAC1D,IAAI,IAAI,CAACd,cAAc,EAAE;oBACvBgB,QAAQC,GAAG,CAACW,gCAAgC,GAAGC,KAAKC,SAAS,CAAChB;gBAChE;gBAEA,IAAI,CAAC,IAAI,CAAC7C,OAAO,CAACkC,GAAG,EAAE;oBACrB,IAAIW,KAAKiB,MAAM,KAAK,cAAc;wBAChC,IAAI,CAACf,QAAQC,GAAG,CAACW,gCAAgC,EAAE;4BACjDI,KAAIC,IAAI,CACN,CAAC,kHAAkH,CAAC;wBAExH;oBACF,OAAO,IAAInB,KAAKiB,MAAM,KAAK,UAAU;wBACnC,MAAM,IAAIG,MACR,CAAC,mGAAmG,CAAC;oBAEzG;gBACF;gBAEA,IAAI,CAAClD,MAAM,GAAG,MAAM,IAAI,CAACqB,YAAY,CAAC;oBACpC,GAAG,IAAI,CAACpC,OAAO;oBACf6C;gBACF;gBACA,IAAI,IAAI,CAACxB,mBAAmB,EAAE;oBAC5B,IAAI,CAACN,MAAM,CAACI,cAAc,CAAC,IAAI,CAACE,mBAAmB;gBACrD;gBACA,OAAO,IAAI,CAACN,MAAM;YACpB;QACF;QACA,OAAO,IAAI,CAAC0C,aAAa;IAC3B;IAEA,MAAc9C,0BAA0B;QACtC,IAAI,IAAI,CAACuD,UAAU,EAAE,OAAO,IAAI,CAACA,UAAU;QAE3C,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;YAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACnD,SAAS,GAAG0C,IAAI,CAAC,CAAC3C;gBAC9C,IAAI,CAACmD,UAAU,GAAG3D,IAAAA,iBAAS,IAAG6D,IAAI,CAChC3D,0BAAc,CAACE,uBAAuB,EACtCI,OAAOZ,iBAAiB,GAAGkE,IAAI,CAACtD;gBAElC,OAAO,IAAI,CAACoD,iBAAiB;gBAC7B,OAAO,IAAI,CAACD,UAAU;YACxB;QACF;QACA,OAAO,IAAI,CAACC,iBAAiB;IAC/B;AACF;AAEA,MAAMG,yBAAyBjF;IAS7B,MAAMwC,UAAU;QACd,MAAM,EAAE0C,kBAAkB,EAAE,GAC1B5E,QAAQ;QAEV,MAAM6E,kBAAkB,CAAC,CAACC,IAAAA,yBAAkB;QAE5C,MAAMC,aAAa,MAAMH,mBAAmB;YAC1CjC,KAAK,IAAI,CAACtC,OAAO,CAACsC,GAAG;YACrBpC,MAAM,IAAI,CAACF,OAAO,CAACE,IAAI,IAAI;YAC3ByE,OAAO,CAAC,CAAC,IAAI,CAAC3E,OAAO,CAACkC,GAAG;YACzBjC,UAAU,IAAI,CAACD,OAAO,CAACC,QAAQ,IAAI;YACnC2E,aAAa,IAAI,CAAC5E,OAAO,CAAC4E,WAAW;YACrCJ,iBAAiB,CAAC,CAACA;QACrB;QACA,IAAI,CAAC9D,cAAc,GAAGgE,UAAU,CAAC,EAAE;QACnC,IAAI,CAACG,cAAc,GAAGH,UAAU,CAAC,EAAE;IACrC;IAEQI,sBACNC,YAAoC,EACpCC,IAAsB,EACtB;QACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;gBAEI;YAD/B,IAAI,CAACA,iBAAiB,GAAG;YACzBF,eAAeA,iBAAgB,CAAA,QAACC,wBAAAA,KAAMnE,MAAM,AAAO,qBAApB,MAAuBE,MAAM;YAE5D,IAAIgE,cAAc;gBAChBA,aAAaG,EAAE,CAAC,WAAW,OAAO9E,KAAKS,QAAQC;oBAC7C,IAAI,CAAC+D,cAAc,CAACzE,KAAKS,QAAQC;gBACnC;YACF;QACF;IACF;IAEAX,oBAAoB;QAClB,OAAO,OACLC,KACAC,KACAC;YAEA,IAAI,CAACwE,qBAAqB,CAAC,IAAI,CAAC9E,OAAO,CAACmF,UAAU,EAAE/E;YAEpD,IAAIE,WAAW;gBACbF,IAAIgF,GAAG,GAAGC,IAAAA,oBAAS,EAAC/E;YACtB;YAEA,OAAO,IAAI,CAACI,cAAc,CAACN,KAAKC;QAClC;IACF;IAEA,MAAMmB,OAAO,GAAGD,IAAkC,EAAE;QAClD,IAAI,CAACnB,KAAKC,KAAKiF,UAAUC,OAAOjF,UAAU,GAAGiB;QAC7C,IAAI,CAACuD,qBAAqB,CAAC,IAAI,CAAC9E,OAAO,CAACmF,UAAU,EAAE/E;QAEpD,IAAI,CAACkF,SAASE,UAAU,CAAC,MAAM;YAC7BC,QAAQC,KAAK,CAAC,CAAC,8BAA8B,EAAEJ,SAAS,CAAC,CAAC;YAC1DA,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC;QAC3B;QACAA,WAAWA,aAAa,WAAW,MAAMA;QAEzClF,IAAIgF,GAAG,GAAGC,IAAAA,oBAAS,EAAC;YAClB,GAAG/E,SAAS;YACZgF;YACAC;QACF;QAEA,MAAM,IAAI,CAAC7E,cAAc,CAACN,KAAYC;QACtC;IACF;;;aA5EU0B,iBAAiB;aACnBkD,oBAA6B;;AA4EvC;AAEA,yDAAyD;AACzD,SAAS7C,aAAapC,OAA0B;IAC9C,8CAA8C;IAC9C,IACEA,WACA,gBAAgBA,WAChB,aAAa,AAACA,QAAgB2F,UAAU,EACxC;QACA,OAAOhG,QAAQ,qBAAqBiG,cAAc,CAAC5F;IACrD;IAEA,IAAIA,WAAW,MAAM;QACnB,MAAM,IAAIiE,MACR;IAEJ;IAEA,IACE,CAAE,CAAA,sBAAsBjE,OAAM,KAC9B+C,QAAQC,GAAG,CAACC,QAAQ,IACpB,CAAC;QAAC;QAAc;QAAe;KAAO,CAAC4C,QAAQ,CAAC9C,QAAQC,GAAG,CAACC,QAAQ,GACpE;QACAc,KAAIC,IAAI,CAAC8B,gCAAqB;IAChC;IAEA,IAAI9F,QAAQkC,GAAG,IAAI,OAAOlC,QAAQkC,GAAG,KAAK,WAAW;QACnDuD,QAAQzB,IAAI,CACV;IAEJ;IAEA,qDAAqD;IACrD,IAAIhE,QAAQ+E,YAAY,KAAK,OAAO;QAClC,MAAMzC,MAAM5C,IAAAA,aAAO,EAACM,QAAQsC,GAAG,IAAI;QAEnC,OAAO,IAAIgC,iBAAiB;YAC1B,GAAGtE,OAAO;YACVsC;QACF;IACF;IAEA,+EAA+E;IAC/E,OAAO,IAAIjD,WAAWW;AACxB;AAEA,qCAAqC;AACrC+F,OAAOC,OAAO,GAAG5D;MAIjB,WAAeA"}