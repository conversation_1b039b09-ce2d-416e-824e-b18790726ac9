{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["loadManifestWithRetries", "loadComponents", "manifestPath", "attempts", "loadManifest", "err", "wait", "loadClientReferenceManifest", "entryName", "process", "env", "NEXT_MINIMAL", "__non_webpack_require__", "require", "globalThis", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "requirePage", "ComponentMod", "hasClientManifest", "endsWith", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "join", "BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "replace", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "catch", "Component", "interopDefault", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;;;;;;;;;;;;IAoEsBA,uBAAuB;eAAvBA;;IAiHTC,cAAc;eAAdA;;;2BAjKN;sBACc;yBACO;gCACG;wBACL;4BACS;8BACN;sBACR;AAyCd,eAAeD,wBACpBE,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOC,IAAAA,0BAAY,EAACF;QACtB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAEA,eAAeC,4BACbL,YAAoB,EACpBM,SAAiB;IAEjBC,QAAQC,GAAG,CAACC,YAAY,GAEpBC,wBAAwBV,gBACxBW,QAAQX;IACZ,IAAI;QACF,OAAO,AAACY,WAAmBC,cAAc,CACvCP,UACD;IACH,EAAE,OAAOH,KAAK;QACZ,OAAOW;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EAKV;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACF,WAAW;QACb,CAACC,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,cAAcT,SAAS;YAChEK,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,SAAST,SAAS;SAC5D;IACH;IACA,MAAMU,eAAe,MAAML,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChDC,IAAAA,oBAAW,EAACR,MAAMD,SAASE;IAG7B,6DAA6D;IAC7D,MAAMS,oBACJT,aACCD,CAAAA,KAAKW,QAAQ,CAAC,YAAYX,SAAS,gBAAgBA,SAAS,aAAY;IAE3E,MAAM,CACJY,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMX,QAAQC,GAAG,CAAC;QACpBxB,wBAAuCmC,IAAAA,UAAI,EAACjB,SAASkB,yBAAc;QACnEpC,wBACEmC,IAAAA,UAAI,EAACjB,SAASmB,kCAAuB;QAEvCR,oBACItB,4BACE4B,IAAAA,UAAI,EACFjB,SACA,UACA,OACAC,KAAKmB,OAAO,CAAC,QAAQ,OAAO,MAAMC,oCAAyB,GAAG,QAEhEpB,KAAKmB,OAAO,CAAC,QAAQ,QAEvBtB;QACJI,YACIpB,wBACEmC,IAAAA,UAAI,EAACjB,SAAS,UAAUsB,oCAAyB,GAAG,UACpDC,KAAK,CAAC,IAAM,QACd;KACL;IAED,MAAMC,YAAYC,IAAAA,8BAAc,EAACf;IACjC,MAAMgB,WAAWD,IAAAA,8BAAc,EAACtB;IAChC,MAAMwB,MAAMF,IAAAA,8BAAc,EAACrB;IAE3B,MAAM,EAAEwB,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvErB;IAEF,OAAO;QACLiB;QACAD;QACAF;QACAX;QACAC;QACAkB,YAAYtB,aAAauB,MAAM,IAAI,CAAC;QACpCvB;QACAkB;QACAC;QACAC;QACAf;QACAC;QACAd;QACAD;QACA8B;IACF;AACF;AAEO,MAAMhD,iBAAiBmD,IAAAA,iBAAS,IAAGC,IAAI,CAC5CC,8BAAkB,CAACrD,cAAc,EACjCgB"}