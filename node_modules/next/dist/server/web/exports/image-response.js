// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _imageresponse.ImageResponse;
    }
});
const _imageresponse = require("../spec-extension/image-response");

//# sourceMappingURL=image-response.js.map