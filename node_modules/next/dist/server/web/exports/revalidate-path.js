// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _revalidatepath.revalidatePath;
    }
});
const _revalidatepath = require("../spec-extension/revalidate-path");

//# sourceMappingURL=revalidate-path.js.map