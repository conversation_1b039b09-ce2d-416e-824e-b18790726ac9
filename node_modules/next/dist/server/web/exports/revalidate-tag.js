// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _revalidatetag.revalidateTag;
    }
});
const _revalidatetag = require("../spec-extension/revalidate-tag");

//# sourceMappingURL=revalidate-tag.js.map