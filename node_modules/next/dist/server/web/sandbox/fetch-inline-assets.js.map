{"version": 3, "sources": ["../../../../src/server/web/sandbox/fetch-inline-assets.ts"], "names": ["fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "hash", "replace", "asset", "assets", "find", "x", "name", "filePath", "resolve", "distDir", "fileIsReadable", "fs", "access", "then", "readStream", "createReadStream", "context", "Response", "requestToBodyStream", "Uint8Array"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;oBAT2B;6BACb;sBACZ;AAOjB,eAAeA,iBAAiBC,OAKtC;QAOeA;IANd,MAAMC,cAAcC,OAAOF,QAAQG,KAAK;IACxC,IAAI,CAACF,YAAYG,UAAU,CAAC,UAAU;QACpC;IACF;IAEA,MAAMC,OAAOJ,YAAYK,OAAO,CAAC,SAAS;IAC1C,MAAMC,SAAQP,kBAAAA,QAAQQ,MAAM,qBAAdR,gBAAgBS,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKN;IACrD,IAAI,CAACE,OAAO;QACV;IACF;IAEA,MAAMK,WAAWC,IAAAA,aAAO,EAACb,QAAQc,OAAO,EAAEP,MAAMK,QAAQ;IACxD,MAAMG,iBAAiB,MAAMC,YAAE,CAACC,MAAM,CAACL,UAAUM,IAAI,CACnD,IAAM,MACN,IAAM;IAGR,IAAIH,gBAAgB;QAClB,MAAMI,aAAaC,IAAAA,oBAAgB,EAACR;QACpC,OAAO,IAAIZ,QAAQqB,OAAO,CAACC,QAAQ,CACjCC,IAAAA,gCAAmB,EAACvB,QAAQqB,OAAO,EAAEG,YAAYL;IAErD;AACF"}