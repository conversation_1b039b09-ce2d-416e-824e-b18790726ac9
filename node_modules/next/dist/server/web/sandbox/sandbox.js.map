{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "includes", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "requestToBodyStream", "headerName", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAOaA,WAAW;eAAXA;;IA8CSC,iBAAiB;eAAjBA;;IA2BTC,GAAG;eAAHA;;;yBA7EoB;6BACG;kCACC;AAE9B,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAaD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEO,eAAeL,kBAAkBW,MAQvC;IACC,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,UAAUZ,OAAOY,QAAQ,KAAK;QAC9BC,mBAAmBb,OAAOa,iBAAiB;QAC3CC,SAASd,OAAOc,OAAO;IACzB;IAEA,IAAId,OAAOe,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGlB,OAAOe,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAanB,OAAOoB,KAAK,CAAE;QACpCb,kBAAkBY;IACpB;IACA,OAAOb;AACT;AAEO,MAAMhB,MAAMG,iBAAiB,eAAe4B,oBAAoBrB,MAAM;QAqBvEA;IApBJ,MAAMM,UAAU,MAAMjB,kBAAkBW;IACxC,MAAMsB,SAAStB,OAAOuB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IACvE,IAAID,YAAYE,QAAQ,CAAC3B,OAAOU,IAAI,GAAG;QACrC,OAAO;YACLP,WAAWyB,QAAQC,OAAO;YAC1BC,UAAU,IAAIxB,QAAQU,OAAO,CAACe,QAAQ,CAAC,MAAM;gBAC3CP,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMQ,eAGJ1B,QAAQU,OAAO,CAACiB,QAAQ,CAAC,CAAC,WAAW,EAAEjC,OAAOU,IAAI,CAAC,CAAC,CAAC,CAACwB,OAAO;IAE/D,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACR,QAAQ,CAAC3B,OAAOuB,OAAO,CAACa,MAAM,KAC1DpC,uBAAAA,OAAOuB,OAAO,CAACc,IAAI,qBAAnBrC,qBAAqBsC,eAAe,KACpCC;IAEJ,MAAMC,cAAclC,QAAQmC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAI3C,OAAOuB,OAAO,CAACqB,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpD/C,OAAOuB,OAAO,CAACqB,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,IAAI;QACF,MAAM9C,SAAS,MAAM8B,aAAa;YAChCT,SAAS;gBACP,GAAGvB,OAAOuB,OAAO;gBACjBc,MACEF,UAAUc,IAAAA,gCAAmB,EAAC3C,QAAQU,OAAO,EAAEwB,aAAaL;YAChE;QACF;QACA,KAAK,MAAMe,cAAc1D,kBAAmB;YAC1CU,OAAO4B,QAAQ,CAACN,OAAO,CAACsB,MAAM,CAACI;QACjC;QACA,OAAOhD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOuB,OAAO,CAACc,IAAI,qBAAnBrC,sBAAqBmD,QAAQ;IACrC;AACF"}