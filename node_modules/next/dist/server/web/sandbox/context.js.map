{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["clearModuleContext", "getModuleContext", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "moduleContexts", "Map", "pendingModuleCaches", "path", "intervalsManager", "removeAll", "timeouts<PERSON><PERSON><PERSON>", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "pick", "BufferImplementation", "EventsImplementation", "AsyncHooksImplementation", "AssertImplementation", "UtilImplementation", "entries", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "err", "message", "stack", "__Request", "Request", "constructor", "validateURL", "next", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "AsyncLocalStorage", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "readFileSync", "runInContext", "filename"], "mappings": ";;;;;;;;;;;;;;;IA2DsBA,kBAAkB;eAAlBA;;IA0YAC,gBAAgB;eAAhBA;;;6BA/bY;2BAI3B;6BACqB;oBACiB;uBACjB;sBACP;mCACY;oBACJ;mEACI;mEACA;mEACA;iEACF;wEACM;kCACa;;;;;;AAQlD,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBAAsBI,WAAWJ,mBAAmB;AACtD,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACM,OAAcC,IAAcD;AACrD;AAEA;;;;CAIC,GACD,MAAME,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAUzB,eAAeZ,mBAAmBc,IAAY;IACnDC,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IAEzB,MAAME,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACT,OAAO;YAC1BO,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIT,eAAgB;QACzCO,cAAcC,KAAKC,OAAOT;IAC5B;IACA,KAAK,MAAM,CAACQ,KAAKC,MAAM,IAAIP,oBAAqB;QAC9CK,cAAcC,KAAK,MAAMC,OAAOP;IAClC;AACF;AAEA,eAAeY,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,UAAS,MAAMC,YAAYC,OAAO,CACtC,MAAMC,YAAE,CAACC,QAAQ,CAACL,QAAQM,QAAQ;QAEpCV,OAAO,CAACI,QAAQO,IAAI,CAAC,GAAGN;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASY;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACtC,QAAQC,GAAG,EAAEyB,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKf,QAAQC,GAAG,CAACc,IAAI;SAAC;IAC3E,MAAMd,MAAMoC,OAAOE,WAAW,CAACH;IAC/BnC,IAAIuC,YAAY,GAAG;IACnB,OAAOvC;AACT;AAEA,SAASwC,yBAAyBP,IAAY;IAC5C,MAAM7B,QACJ,IAAIqC,MAAM,CAAC,uBAAuB,EAAER,KAAK;8DACiB,CAAC;IAC7DnC,oBAAoBM,OAAOsC,yBAAc,CAACC,UAAU;IACpD,MAAMvC;AACR;AAEA,SAASwC;IACP,MAAMC,kBAAkB;QAAE7C,KAAKkC;IAAgC;IAC/D,MAAMY,iBAAsC,CAAC;IAC7C,KAAK,MAAMhC,OAAOsB,OAAOC,IAAI,CAACtC,SAAU;QACtC,IAAIe,QAAQ,OAAO;QACnBsB,OAAOW,cAAc,CAACF,iBAAiB/B,KAAK;YAC1CkC;gBACE,IAAIF,cAAc,CAAChC,IAAI,KAAKmC,WAAW;oBACrC,OAAOH,cAAc,CAAChC,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACf,OAAe,CAACe,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM0B,yBAAyB,CAAC,QAAQ,EAAE1B,IAAI,CAAC;gBACxD;gBACA,OAAOmC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAAChC,IAAI,GAAGqC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQrC,OAA+B,EAAEiB,IAAY;IAC5DG,OAAOW,cAAc,CAAC/B,SAASiB,MAAM;QACnCe;YACE,OAAO;gBACLR,yBAAyBP;YAC3B;QACF;QACAmB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACrD;QACN,IAAIA,iBAAiBoD,kBAAkB;YACrC1D,oBAAoBM,OAAOsC,yBAAc,CAACC,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C1D,oBAAoB6D,SAASC,MAAM,EAAElB,yBAAc,CAACC,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAeC,IAAAA,UAAI,EAACC,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeD,IAAAA,UAAI,EAACE,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBF,IAAAA,UAAI,EAACG,wBAAwB,EAAE;YACjD;YACA;SACD;QACD,eAAeH,IAAAA,UAAI,EAACI,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaJ,IAAAA,UAAI,EAACK,iBAAkB,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAI7D,IAAI6B,OAAOiC,OAAO,CAACP;AAChC,CAAA;AAEA;;;CAGC,GACD,eAAeQ,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAMpD,OAAO,MAAMD,SAASmD,QAAQI,iBAAiB,CAACtD,IAAI,IAAI,EAAE;IAChE,MAAMkC,UAAU,IAAIqB,wBAAW,CAAC;QAC9BC,gBACE9E,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAE6E,SAAS;YAAMzD,MAAM;QAAK,IAC5B4B;QACN8B,QAAQ,CAAC/D;YACPA,QAAQjB,OAAO,GAAG6C;YAElBR,OAAOW,cAAc,CAAC/B,SAAS,WAAW;gBACxCoC,YAAY;gBACZD,OAAO,CAAC6B;oBACN,MAAM7B,QAAQU,gBAAgBb,GAAG,CAACgC;oBAClC,IAAI,CAAC7B,OAAO;wBACV,MAAM8B,UAAU,8BAA8BD;oBAChD;oBACA,OAAO7B;gBACT;YACF;YAEAnC,QAAQkE,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAMrE,MAAMqE,GAAGC,QAAQ;gBACvB,IAAI,CAACZ,YAAYtD,GAAG,CAACJ,MAAM;oBACzB,MAAMuE,UAAUxF,eACd,IAAI4C,MACF,CAAC;yEAC0D,CAAC,GAE9DC,yBAAc,CAACC,UAAU;oBAE3B0C,QAAQpD,IAAI,GAAG;oBACfQ,MAAM6C,iBAAiB,CAACD,SAASH;oBACjCV,YAAYe,GAAG,CAACzE;oBAChByD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEAnE,QAAQyE,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAMrE,MAAMqE,GAAGC,QAAQ;gBACvB,IAAI,CAACV,mBAAmBxD,GAAG,CAACJ,MAAM;oBAChC,MAAMuE,UAAUxF,eACd,IAAI4C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B0C,QAAQpD,IAAI,GAAG;oBACfQ,MAAM6C,iBAAiB,CAACD,SAASI;oBACjCf,mBAAmBa,GAAG,CAACzE;oBACvByD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEFnE,QAAQ0E,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAM/E,MAAMqE,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAAClB,mBAAmBxD,GAAG,CAACJ,MAAM;oBAC1D,MAAMuE,UAAUxF,eACd,IAAI4C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B0C,QAAQpD,IAAI,GAAG;oBACfQ,MAAM6C,iBAAiB,CAACD,SAASK;oBACjChB,mBAAmBa,GAAG,CAACzE;oBACvByD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAU9E,QAAQ+E,KAAK;YAC7B/E,QAAQ+E,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBAcnCA;gBAbF,MAAMC,eAAe,IAAIzD,MAAM;gBAC/B,MAAM0D,gBAAgB,MAAMC,IAAAA,mCAAgB,EAAC;oBAC3CJ;oBACAK,QAAQ9B,QAAQI,iBAAiB,CAAC0B,MAAM;oBACxCC,SAAS/B,QAAQ+B,OAAO;oBACxBtF;gBACF;gBACA,IAAImF,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKM,OAAO,GAAG,IAAIC,QAAQP,KAAKM,OAAO,IAAI,CAAC;gBAC5C,MAAME,QACJR,EAAAA,oBAAAA,KAAKM,OAAO,CAACvD,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1CiD,kBAA6CS,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAMvD,QAAQsD,MAAME,MAAM,CAACpC,QAAQqC,UAAU,EAAEC,IAAI,CAAC;gBACpDZ,KAAKM,OAAO,CAACrD,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAAC8C,KAAKM,OAAO,CAACrF,GAAG,CAAC,eAAe;oBACnC+E,KAAKM,OAAO,CAACrD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM4D,WACJ,OAAOd,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMe,GAAG,EAAE;oBACjB,GAAGhD,IAAAA,UAAI,EAACiC,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPM,SAAS;wBACP,GAAGnE,OAAOE,WAAW,CAAC0D,MAAMO,OAAO,CAAC;wBACpC,GAAGnE,OAAOE,WAAW,CAAC2D,KAAKM,OAAO,CAAC;oBACrC;gBACF,KACAT,QAAQkB,OAAOhB,QAAQC;gBAE7B,OAAO,MAAMa,SAASG,KAAK,CAAC,CAACC;oBAC3BhB,aAAaiB,OAAO,GAAGD,IAAIC,OAAO;oBAClCD,IAAIE,KAAK,GAAGlB,aAAakB,KAAK;oBAC9B,MAAMF;gBACR;YACF;YAEA,MAAMG,YAAYrG,QAAQsG,OAAO;YACjCtG,QAAQsG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYvB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMc,MACJ,OAAOf,UAAU,YAAY,SAASA,QAClCA,MAAMe,GAAG,GACTC,OAAOhB;oBACbwB,IAAAA,kBAAW,EAACT;oBACZ,KAAK,CAACA,KAAKd;oBACX,IAAI,CAACwB,IAAI,GAAGxB,wBAAAA,KAAMwB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAa1G,QAAQ2G,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAC7G,QAAQ2G,QAAQ;YAClE3G,QAAQ2G,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BN,IAAAA,kBAAW,EAACM,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAM7F,QAAQ8F,qCAA0B,CAAE;gBAC7C1E,QAAQrC,SAASiB;YACnB;YAEAG,OAAO4F,MAAM,CAAChH,SAASK;YAEvBL,QAAQiH,iBAAiB,GAAGA,8BAAiB;YAE7C,+DAA+D;YAC/DjH,QAAQkH,WAAW,GAAG,CAAC,GAAGJ,OACxBpH,kCAAgB,CAAC6E,GAAG,CAACuC;YAEvB,+DAA+D;YAC/D9G,QAAQmH,aAAa,GAAG,CAACC,WACvB1H,kCAAgB,CAAC2H,MAAM,CAACD;YAE1B,+DAA+D;YAC/DpH,QAAQsH,UAAU,GAAG,CAAC,GAAGR,OACvBlH,iCAAe,CAAC2E,GAAG,CAACuC;YAEtB,+DAA+D;YAC/D9G,QAAQuH,YAAY,GAAG,CAACC,UACtB5H,iCAAe,CAACyH,MAAM,CAACG;YAEzB,OAAOxH;QACT;IACF;IAEA,MAAMyH,yBAAyBnF,0BAA0BC;IACzDA,QAAQvC,OAAO,CAAC0H,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6BjF,8BAA8BH;IACjEA,QAAQvC,OAAO,CAAC0H,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACLpF;QACAtC,OAAO,IAAIV;QACXiE,aAAa,IAAIC;IACnB;AACF;AAUA,SAASmE,uBAAuBrE,OAA6B;IAC3D,IAAIsE,wBAAwBrI,oBAAoBwC,GAAG,CAACuB,QAAQqC,UAAU;IACtE,IAAI,CAACiC,uBAAuB;QAC1BA,wBAAwBvE,oBAAoBC;QAC5C/D,oBAAoB0C,GAAG,CAACqB,QAAQqC,UAAU,EAAEiC;IAC9C;IACA,OAAOA;AACT;AAQO,eAAejJ,iBAAiB2E,OAA6B;IAMlE,IAAIuE;IAIJ,IAAIvE,QAAQwE,QAAQ,EAAE;QACpBD,oBACExI,eAAe0C,GAAG,CAACuB,QAAQqC,UAAU,KACpC,MAAMgC,uBAAuBrE;IAClC;IAEA,IAAI,CAACuE,mBAAmB;QACtBA,oBAAoB,MAAMxE,oBAAoBC;QAC9CjE,eAAe4C,GAAG,CAACqB,QAAQqC,UAAU,EAAEkC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAc/H,KAAK,CAACC,GAAG,CAACgI,WAAW;YACtC,MAAMC,UAAUC,IAAAA,gBAAY,EAACF,UAAU;YACvC,IAAI;gBACFG,IAAAA,gBAAY,EAACF,SAASH,cAAczF,OAAO,CAACvC,OAAO,EAAE;oBACnDsI,UAAUJ;gBACZ;gBACAF,cAAc/H,KAAK,CAACiC,GAAG,CAACgG,UAAUC;YACpC,EAAE,OAAO/I,OAAO;gBACd,IAAImE,QAAQwE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAe/H,KAAK,CAACE,MAAM,CAAC+H;gBAC9B;gBACA,MAAM9I;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAG4I,aAAa;QAAEC;IAAkB;AAC/C"}