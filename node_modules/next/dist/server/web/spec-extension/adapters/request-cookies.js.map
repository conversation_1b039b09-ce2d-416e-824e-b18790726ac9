{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/request-cookies.ts"], "names": ["ReadonlyRequestCookiesError", "RequestCookiesAdapter", "getModifiedCookieValues", "appendMutableCookies", "MutableRequestCookiesAdapter", "Error", "constructor", "callable", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "modified", "Array", "isArray", "length", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "ResponseCookies", "returnedCookies", "getAll", "cookie", "set", "wrap", "onUpdateCookies", "response<PERSON><PERSON><PERSON>", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "staticGenerationAsyncStore", "fetch", "__nextGetStaticStore", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "args", "add", "delete"], "mappings": ";;;;;;;;;;;;;;;;;;IASaA,2BAA2B;eAA3BA;;IAqBAC,qBAAqB;eAArBA;;IAmBGC,uBAAuB;eAAvBA;;IAaAC,oBAAoB;eAApBA;;IAgCHC,4BAA4B;eAA5BA;;;yBA3FmB;yBACD;AAKxB,MAAMJ,oCAAoCK;IAC/CC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIP;IACZ;AACF;AAWO,MAAMC;IACX,OAAcO,KAAKC,OAAuB,EAA0B;QAClE,OAAO,IAAIC,MAAMD,SAAgB;YAC/BE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOb,4BAA4BO,QAAQ;oBAC7C;wBACE,OAAOQ,uBAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF;AAEA,MAAME,8BAA8BC,OAAOC,GAAG,CAAC;AAExC,SAAShB,wBACdO,OAAwB;IAExB,MAAMU,WAAyC,AAACV,OAA0B,CACxEO,4BACD;IACD,IAAI,CAACG,YAAY,CAACC,MAAMC,OAAO,CAACF,aAAaA,SAASG,MAAM,KAAK,GAAG;QAClE,OAAO,EAAE;IACX;IAEA,OAAOH;AACT;AAEO,SAAShB,qBACdoB,OAAgB,EAChBC,cAA+B;IAE/B,MAAMC,uBAAuBvB,wBAAwBsB;IACrD,IAAIC,qBAAqBH,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,uDAAuD;IACvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAMI,aAAa,IAAIC,wBAAe,CAACJ;IACvC,MAAMK,kBAAkBF,WAAWG,MAAM;IAEzC,yCAAyC;IACzC,KAAK,MAAMC,UAAUL,qBAAsB;QACzCC,WAAWK,GAAG,CAACD;IACjB;IAEA,gDAAgD;IAChD,KAAK,MAAMA,UAAUF,gBAAiB;QACpCF,WAAWK,GAAG,CAACD;IACjB;IAEA,OAAO;AACT;AAMO,MAAM1B;IACX,OAAc4B,KACZvB,OAAuB,EACvBwB,eAA6C,EAC5B;QACjB,MAAMC,iBAAiB,IAAIP,wBAAe,CAAC,IAAIQ;QAC/C,KAAK,MAAML,UAAUrB,QAAQoB,MAAM,GAAI;YACrCK,eAAeH,GAAG,CAACD;QACrB;QAEA,IAAIM,iBAAmC,EAAE;QACzC,MAAMC,kBAAkB,IAAIC;QAC5B,MAAMC,wBAAwB;gBAEO;YADnC,gEAAgE;YAChE,MAAMC,6BAA6B,AAACC,MACjCC,oBAAoB,qBADY,8BAAA,AAACD,MACjCC,oBAAoB,MADaD,2BAAD,4BAE/BE,QAAQ;YACZ,IAAIH,4BAA4B;gBAC9BA,2BAA2BI,kBAAkB,GAAG;YAClD;YAEA,MAAMC,aAAaX,eAAeL,MAAM;YACxCO,iBAAiBS,WAAWC,MAAM,CAAC,CAACC,IAAMV,gBAAgBW,GAAG,CAACD,EAAEE,IAAI;YACpE,IAAIhB,iBAAiB;gBACnB,MAAMiB,oBAA8B,EAAE;gBACtC,KAAK,MAAMpB,UAAUM,eAAgB;oBACnC,MAAMe,cAAc,IAAIxB,wBAAe,CAAC,IAAIQ;oBAC5CgB,YAAYpB,GAAG,CAACD;oBAChBoB,kBAAkBE,IAAI,CAACD,YAAYE,QAAQ;gBAC7C;gBAEApB,gBAAgBiB;YAClB;QACF;QAEA,OAAO,IAAIxC,MAAMwB,gBAAgB;YAC/BvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,qDAAqD;oBACrD,KAAKG;wBACH,OAAOoB;oBAET,iEAAiE;oBACjE,yBAAyB;oBACzB,KAAK;wBACH,OAAO,SAAU,GAAGkB,IAAiC;4BACnDjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACL,IAAI;4BAEtD,IAAI;gCACFrC,OAAO4C,MAAM,IAAIF;4BACnB,SAAU;gCACRf;4BACF;wBACF;oBACF,KAAK;wBACH,OAAO,SACL,GAAGe,IAE0B;4BAE7BjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACL,IAAI;4BAEtD,IAAI;gCACF,OAAOrC,OAAOmB,GAAG,IAAIuB;4BACvB,SAAU;gCACRf;4BACF;wBACF;oBACF;wBACE,OAAOxB,uBAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF"}