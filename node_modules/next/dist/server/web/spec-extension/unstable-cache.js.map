{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["unstable_cache", "cb", "keyParts", "options", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "_staticGenerationAsyncStorage", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "postpone", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isUnstableCacheCallback", "isStaticGeneration", "tags", "validateTags", "tag", "includes", "push", "implicitTags", "addImplicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "kindHint", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "CACHE_ONE_YEAR", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;sDAN8D;2BAC/C;4BACe;AAIvC,SAASA,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMC,+BACJ,CAAA,AAACC,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CE,kEAA6B;IAE1E,IAAIJ,QAAQK,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAER,GAAGS,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QACJT,gDAAAA,6BAA8BU,QAAQ;QAExC,IAAID,SAAS,OAAOV,QAAQK,UAAU,KAAK,UAAU;YACnD,yEAAyE;YACzE,IAAIL,QAAQK,UAAU,KAAK,GAAG;gBAC5B,0DAA0D;gBAC1DK,MAAME,QAAQ,oBAAdF,MAAME,QAAQ,MAAdF,OAAiB;gBAEjB,+BAA+B;gBAC/BA,MAAML,UAAU,GAAG;YACnB,gIAAgI;YAClI,OAAO,IAAI,OAAOK,MAAML,UAAU,KAAK,UAAU;gBAC/C,IAAIK,MAAML,UAAU,GAAGL,QAAQK,UAAU,EAAE;oBACzCK,MAAML,UAAU,GAAGL,QAAQK,UAAU;gBACvC;YACA,oDAAoD;YACtD,OAAO;gBACLK,MAAML,UAAU,GAAGL,QAAQK,UAAU;YACvC;QACF;QAEA,MAAMQ,mBAGJH,CAAAA,yBAAAA,MAAOG,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIP,MACR,CAAC,sDAAsD,EAAER,GAAGS,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMS,YAAY,CAAC,EAAElB,GAAGS,QAAQ,GAAG,CAAC,EAClCU,MAAMC,OAAO,CAACnB,aAAaA,SAASoB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACZ,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOR,6BAA6BqB,GAAG,CACrC;YACE,GAAGZ,KAAK;YACR,8DAA8D;YAC9D,8CAA8C;YAC9Ca,YAAY;YACZC,aAAad,CAAAA,yBAAAA,MAAOc,WAAW,KAAI;YACnCC,yBAAyB;YACzBC,oBAAoBhB,CAAAA,yBAAAA,MAAOgB,kBAAkB,MAAK;YAClDd,QAAQ,EAAEF,yBAAAA,MAAOE,QAAQ;QAC3B,GACA;YACE,MAAMe,OAAOC,IAAAA,wBAAY,EACvB5B,QAAQ2B,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAE7B,GAAGS,QAAQ,GAAG,CAAC;YAGnC,IAAIU,MAAMC,OAAO,CAACS,SAASjB,OAAO;gBAChC,IAAI,CAACA,MAAMiB,IAAI,EAAE;oBACfjB,MAAMiB,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAME,OAAOF,KAAM;oBACtB,IAAI,CAACjB,MAAMiB,IAAI,CAACG,QAAQ,CAACD,MAAM;wBAC7BnB,MAAMiB,IAAI,CAACI,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAetB,QAAQuB,IAAAA,2BAAe,EAACvB,SAAS,EAAE;YAExD,MAAMwB,WAAW,OAAMrB,oCAAAA,iBAAkBsB,aAAa,CAACnB;YACvD,MAAMoB,aACJF,YACA,sDAAsD;YACtD,4CAA4C;YAC5CxB,CAAAA,yBAAAA,MAAOa,UAAU,MAAK,oBACtB,CACEb,CAAAA,CAAAA,yBAAAA,MAAO2B,oBAAoB,KAAIxB,iBAAiBwB,oBAAoB,AAAD,KAEpE,OAAMxB,oCAAAA,iBAAkByB,GAAG,CAACJ,UAAU;gBACrCK,UAAU;gBACVlC,YAAYL,QAAQK,UAAU;gBAC9BsB;gBACAa,UAAUR;YACZ;YAEF,MAAMS,iBAAiB;gBACrB,MAAMC,SAAS,MAAM5C,MAAMW;gBAE3B,IAAIyB,YAAYrB,kBAAkB;oBAChC,MAAMA,iBAAiB8B,GAAG,CACxBT,UACA;wBACEU,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAM3B,KAAKC,SAAS,CAACqB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACA5C,YACE,OAAOL,QAAQK,UAAU,KAAK,WAC1B6C,yBAAc,GACdlD,QAAQK,UAAU;oBAC1B,GACA;wBACEA,YAAYL,QAAQK,UAAU;wBAC9BkB,YAAY;wBACZI;oBACF;gBAEJ;gBACA,OAAOe;YACT;YAEA,IAAI,CAACN,cAAc,CAACA,WAAWe,KAAK,EAAE;gBACpC,OAAOV;YACT;YAEA,IAAIL,WAAWe,KAAK,CAACP,IAAI,KAAK,SAAS;gBACrCQ,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAErC,UAAU,CAAC;gBAE1D,OAAOyB;YACT;YACA,IAAIa;YACJ,MAAMC,UAAUnB,WAAWmB,OAAO;YAElC,IAAInB,YAAY;gBACd,MAAMoB,UAAUpB,WAAWe,KAAK,CAACN,IAAI;gBACrCS,cAAclC,KAAKqC,KAAK,CAACD,QAAQT,IAAI;YACvC;YAEA,IAAIQ,SAAS;gBACX,IAAI,CAAC7C,OAAO;oBACV,OAAO+B;gBACT,OAAO;oBACL,IAAI,CAAC/B,MAAMgD,kBAAkB,EAAE;wBAC7BhD,MAAMgD,kBAAkB,GAAG,CAAC;oBAC9B;oBACAhD,MAAMgD,kBAAkB,CAAC1C,UAAU,GAAGyB,iBAAiBkB,KAAK,CAC1D,CAACC,MACCR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAErC,UAAU,CAAC,EAAE4C;gBAEjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAO9C;AACT"}