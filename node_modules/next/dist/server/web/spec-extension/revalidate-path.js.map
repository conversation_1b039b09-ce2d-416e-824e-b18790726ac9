{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate-path.ts"], "names": ["revalidatePath", "originalPath", "type", "length", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "console", "warn", "normalizedPath", "NEXT_CACHE_IMPLICIT_TAG_ID", "endsWith", "isDynamicRoute", "revalidateTag"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;+BAPc;uBACC;2BAIxB;AAEA,SAASA,eAAeC,YAAoB,EAAEC,IAAwB;IAC3E,IAAID,aAAaE,MAAM,GAAGC,yCAA8B,EAAE;QACxDC,QAAQC,IAAI,CACV,CAAC,kCAAkC,EAAEL,aAAa,+BAA+B,EAAEG,yCAA8B,CAAC,uFAAuF,CAAC;QAE5M;IACF;IAEA,IAAIG,iBAAiB,CAAC,EAAEC,qCAA0B,CAAC,EAAEP,aAAa,CAAC;IAEnE,IAAIC,MAAM;QACRK,kBAAkB,CAAC,EAAEA,eAAeE,QAAQ,CAAC,OAAO,KAAK,IAAI,EAAEP,KAAK,CAAC;IACvE,OAAO,IAAIQ,IAAAA,qBAAc,EAACT,eAAe;QACvCI,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEL,aAAa,kLAAkL,CAAC;IAErO;IACA,OAAOU,IAAAA,4BAAa,EAACJ;AACvB"}