{"version": 3, "sources": ["../../../../src/server/web/spec-extension/response.ts"], "names": ["NextResponse", "INTERNALS", "Symbol", "REDIRECTS", "Set", "handleMiddlewareField", "init", "headers", "request", "Headers", "Error", "keys", "key", "value", "set", "push", "join", "Response", "constructor", "body", "cookies", "ResponseCookies", "url", "NextURL", "toNodeOutgoingHttpHeaders", "nextConfig", "undefined", "for", "bodyUsed", "Object", "fromEntries", "ok", "redirected", "status", "statusText", "type", "json", "response", "redirect", "has", "RangeError", "initObj", "validateURL", "rewrite", "destination", "next"], "mappings": ";;;;+BA4BaA;;;eAAAA;;;yBA3BW;uBAC+B;yBAEvB;AAEhC,MAAMC,YAAYC,OAAO;AACzB,MAAMC,YAAY,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEnD,SAASC,sBACPC,IAAwC,EACxCC,OAAgB;QAEZD;IAAJ,IAAIA,yBAAAA,gBAAAA,KAAME,OAAO,qBAAbF,cAAeC,OAAO,EAAE;QAC1B,IAAI,CAAED,CAAAA,KAAKE,OAAO,CAACD,OAAO,YAAYE,OAAM,GAAI;YAC9C,MAAM,IAAIC,MAAM;QAClB;QAEA,MAAMC,OAAO,EAAE;QACf,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIP,KAAKE,OAAO,CAACD,OAAO,CAAE;YAC/CA,QAAQO,GAAG,CAAC,0BAA0BF,KAAKC;YAC3CF,KAAKI,IAAI,CAACH;QACZ;QAEAL,QAAQO,GAAG,CAAC,iCAAiCH,KAAKK,IAAI,CAAC;IACzD;AACF;AAEO,MAAMhB,qBAAqCiB;IAOhDC,YAAYC,IAAsB,EAAEb,OAAqB,CAAC,CAAC,CAAE;QAC3D,KAAK,CAACa,MAAMb;QAEZ,IAAI,CAACL,UAAU,GAAG;YAChBmB,SAAS,IAAIC,wBAAe,CAAC,IAAI,CAACd,OAAO;YACzCe,KAAKhB,KAAKgB,GAAG,GACT,IAAIC,gBAAO,CAACjB,KAAKgB,GAAG,EAAE;gBACpBf,SAASiB,IAAAA,gCAAyB,EAAC,IAAI,CAACjB,OAAO;gBAC/CkB,YAAYnB,KAAKmB,UAAU;YAC7B,KACAC;QACN;IACF;IAEA,CAACxB,OAAOyB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLP,SAAS,IAAI,CAACA,OAAO;YACrBE,KAAK,IAAI,CAACA,GAAG;YACb,mCAAmC;YACnCH,MAAM,IAAI,CAACA,IAAI;YACfS,UAAU,IAAI,CAACA,QAAQ;YACvBrB,SAASsB,OAAOC,WAAW,CAAC,IAAI,CAACvB,OAAO;YACxCwB,IAAI,IAAI,CAACA,EAAE;YACXC,YAAY,IAAI,CAACA,UAAU;YAC3BC,QAAQ,IAAI,CAACA,MAAM;YACnBC,YAAY,IAAI,CAACA,UAAU;YAC3BC,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEA,IAAWf,UAAU;QACnB,OAAO,IAAI,CAACnB,UAAU,CAACmB,OAAO;IAChC;IAEA,OAAOgB,KACLjB,IAAc,EACdb,IAAmB,EACK;QACxB,MAAM+B,WAAqBpB,SAASmB,IAAI,CAACjB,MAAMb;QAC/C,OAAO,IAAIN,aAAaqC,SAASlB,IAAI,EAAEkB;IACzC;IAEA,OAAOC,SAAShB,GAA2B,EAAEhB,IAA4B,EAAE;QACzE,MAAM2B,SAAS,OAAO3B,SAAS,WAAWA,OAAOA,CAAAA,wBAAAA,KAAM2B,MAAM,KAAI;QACjE,IAAI,CAAC9B,UAAUoC,GAAG,CAACN,SAAS;YAC1B,MAAM,IAAIO,WACR;QAEJ;QACA,MAAMC,UAAU,OAAOnC,SAAS,WAAWA,OAAO,CAAC;QACnD,MAAMC,UAAU,IAAIE,QAAQgC,2BAAAA,QAASlC,OAAO;QAC5CA,QAAQO,GAAG,CAAC,YAAY4B,IAAAA,kBAAW,EAACpB;QAEpC,OAAO,IAAItB,aAAa,MAAM;YAC5B,GAAGyC,OAAO;YACVlC;YACA0B;QACF;IACF;IAEA,OAAOU,QACLC,WAAmC,EACnCtC,IAA6B,EAC7B;QACA,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,wBAAwB4B,IAAAA,kBAAW,EAACE;QAEhDvC,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;IAEA,OAAOsC,KAAKvC,IAA6B,EAAE;QACzC,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,qBAAqB;QAEjCT,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;AACF"}